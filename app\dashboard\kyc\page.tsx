"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, CheckCircle, Clock, FileText, Upload, User, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function KycPage() {
  const [activeTab, setActiveTab] = useState("personal")
  const [kycStatus, setKycStatus] = useState("pending") // pending, in-review, approved, rejected
  const [uploadedFiles, setUploadedFiles] = useState<{ name: string; size: string; status: string }[]>([])

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        status: "uploaded",
      }))
      setUploadedFiles([...uploadedFiles, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    const newFiles = [...uploadedFiles]
    newFiles.splice(index, 1)
    setUploadedFiles(newFiles)
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <CardTitle className="text-white">KYC Verification</CardTitle>
                <CardDescription className="text-gray-400">
                  Complete your identity verification to unlock withdrawals
                </CardDescription>
              </div>
              <div className="mt-4 md:mt-0">
                {kycStatus === "pending" && (
                  <div className="flex items-center px-3 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-sm">
                    <Clock className="h-4 w-4 mr-2" />
                    Not Started
                  </div>
                )}
                {kycStatus === "in-review" && (
                  <div className="flex items-center px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm">
                    <Clock className="h-4 w-4 mr-2" />
                    In Review
                  </div>
                )}
                {kycStatus === "approved" && (
                  <div className="flex items-center px-3 py-1 bg-green-500/20 text-green-400 rounded-full text-sm">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approved
                  </div>
                )}
                {kycStatus === "rejected" && (
                  <div className="flex items-center px-3 py-1 bg-red-500/20 text-red-400 rounded-full text-sm">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Rejected
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-400">Verification Progress</span>
                <span className="text-sm font-medium text-white">
                  {activeTab === "personal" ? "1" : activeTab === "address" ? "2" : "3"}/3
                </span>
              </div>
              <Progress
                value={activeTab === "personal" ? 33.33 : activeTab === "address" ? 66.66 : 100}
                className="h-2 bg-gray-700"
              >
                <div
                  className="h-full bg-teal-500"
                  style={{
                    width: `${activeTab === "personal" ? 33.33 : activeTab === "address" ? 66.66 : 100}%`,
                  }}
                />
              </Progress>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 mb-6 bg-[#001a2c]">
                <TabsTrigger
                  value="personal"
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                >
                  Personal Info
                </TabsTrigger>
                <TabsTrigger value="address" className="data-[state=active]:bg-teal-500 data-[state=active]:text-white">
                  Address
                </TabsTrigger>
                <TabsTrigger
                  value="documents"
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                >
                  Documents
                </TabsTrigger>
              </TabsList>

              <TabsContent value="personal">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-white">
                        First Name
                      </Label>
                      <Input id="firstName" placeholder="John" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-white">
                        Last Name
                      </Label>
                      <Input id="lastName" placeholder="Doe" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="dob" className="text-white">
                        Date of Birth
                      </Label>
                      <Input id="dob" type="date" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="nationality" className="text-white">
                        Nationality
                      </Label>
                      <Select>
                        <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white">
                          <SelectValue placeholder="Select nationality" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                          <SelectItem value="us">United States</SelectItem>
                          <SelectItem value="uk">United Kingdom</SelectItem>
                          <SelectItem value="ca">Canada</SelectItem>
                          <SelectItem value="au">Australia</SelectItem>
                          <SelectItem value="de">Germany</SelectItem>
                          <SelectItem value="fr">France</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white">
                        Phone Number
                      </Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+****************"
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white">
                        Email Address
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      className="bg-teal-500 hover:bg-teal-600 text-white"
                      onClick={() => setActiveTab("address")}
                    >
                      Next Step
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="address">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="address1" className="text-white">
                      Address Line 1
                    </Label>
                    <Input
                      id="address1"
                      placeholder="123 Main St"
                      className="bg-[#001a2c] border-[#003a4c] text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address2" className="text-white">
                      Address Line 2 (Optional)
                    </Label>
                    <Input id="address2" placeholder="Apt 4B" className="bg-[#001a2c] border-[#003a4c] text-white" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="city" className="text-white">
                        City
                      </Label>
                      <Input id="city" placeholder="New York" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state" className="text-white">
                        State/Province
                      </Label>
                      <Input id="state" placeholder="NY" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="zip" className="text-white">
                        Postal/ZIP Code
                      </Label>
                      <Input id="zip" placeholder="10001" className="bg-[#001a2c] border-[#003a4c] text-white" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country" className="text-white">
                        Country
                      </Label>
                      <Select>
                        <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white">
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                          <SelectItem value="us">United States</SelectItem>
                          <SelectItem value="uk">United Kingdom</SelectItem>
                          <SelectItem value="ca">Canada</SelectItem>
                          <SelectItem value="au">Australia</SelectItem>
                          <SelectItem value="de">Germany</SelectItem>
                          <SelectItem value="fr">France</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                      onClick={() => setActiveTab("personal")}
                    >
                      Previous Step
                    </Button>
                    <Button
                      className="bg-teal-500 hover:bg-teal-600 text-white"
                      onClick={() => setActiveTab("documents")}
                    >
                      Next Step
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="documents">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label className="text-white">Identity Document</Label>
                    <div className="border-2 border-dashed border-[#003a4c] rounded-lg p-6 text-center">
                      <div className="flex flex-col items-center">
                        <Upload className="h-10 w-10 text-gray-400 mb-2" />
                        <h3 className="text-white font-medium mb-1">Upload ID Document</h3>
                        <p className="text-gray-400 text-sm mb-4">Passport, Driver's License, or National ID Card</p>
                        <Input
                          type="file"
                          className="hidden"
                          id="id-upload"
                          onChange={handleFileUpload}
                          accept="image/png, image/jpeg, application/pdf"
                        />
                        <label htmlFor="id-upload">
                          <Button
                            variant="outline"
                            className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                            onClick={() => document.getElementById("id-upload")?.click()}
                          >
                            Select File
                          </Button>
                        </label>
                        <p className="text-xs text-gray-500 mt-2">Supported formats: JPG, PNG, PDF. Max size: 5MB</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-white">Proof of Address</Label>
                    <div className="border-2 border-dashed border-[#003a4c] rounded-lg p-6 text-center">
                      <div className="flex flex-col items-center">
                        <FileText className="h-10 w-10 text-gray-400 mb-2" />
                        <h3 className="text-white font-medium mb-1">Upload Address Proof</h3>
                        <p className="text-gray-400 text-sm mb-4">
                          Utility Bill, Bank Statement, or Official Letter (less than 3 months old)
                        </p>
                        <Input
                          type="file"
                          className="hidden"
                          id="address-upload"
                          onChange={handleFileUpload}
                          accept="image/png, image/jpeg, application/pdf"
                        />
                        <label htmlFor="address-upload">
                          <Button
                            variant="outline"
                            className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                            onClick={() => document.getElementById("address-upload")?.click()}
                          >
                            Select File
                          </Button>
                        </label>
                        <p className="text-xs text-gray-500 mt-2">Supported formats: JPG, PNG, PDF. Max size: 5MB</p>
                      </div>
                    </div>
                  </div>

                  {uploadedFiles.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-white">Uploaded Files</Label>
                      <div className="border border-[#003a4c] rounded-lg overflow-hidden">
                        <div className="divide-y divide-[#003a4c]">
                          {uploadedFiles.map((file, index) => (
                            <div key={index} className="flex items-center justify-between p-3">
                              <div className="flex items-center">
                                <FileText className="h-5 w-5 text-gray-400 mr-3" />
                                <div>
                                  <p className="text-white text-sm font-medium">{file.name}</p>
                                  <p className="text-gray-500 text-xs">{file.size}</p>
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-gray-400 hover:text-white"
                                onClick={() => removeFile(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                      onClick={() => setActiveTab("address")}
                    >
                      Previous Step
                    </Button>
                    <Button
                      className="bg-teal-500 hover:bg-teal-600 text-white"
                      onClick={() => setKycStatus("in-review")}
                    >
                      Submit Verification
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Verification Requirements</CardTitle>
            <CardDescription className="text-gray-400">Guidelines for successful identity verification</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Identity Document</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Government-issued ID (passport, driver's license, national ID)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Document must be valid and not expired</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>All corners and edges must be visible</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Information must be clearly readable</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Proof of Address</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Utility bill, bank statement, or official letter</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Must be less than 3 months old</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Must show your full name and address</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Digital bills are acceptable if they show the full page</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Important Notes</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Verification usually takes 1-2 business days</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>All information must match your account details</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Documents must be in color (not black and white)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>File size must be under 5MB per document</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              For any questions regarding the verification process, please contact our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}
