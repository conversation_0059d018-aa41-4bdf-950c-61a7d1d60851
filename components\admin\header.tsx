"use client"

import { useState } from "react"
import { usePathname } from "next/navigation"
import { Bell, ChevronDown, HelpCircle, Search, User, Shield } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface AdminHeaderProps {
  sidebarWidth: number
}

export default function AdminHeader({ sidebarWidth }: AdminHeaderProps) {
  const pathname = usePathname()
  const [searchQuery, setSearchQuery] = useState("")

  // Get page title from pathname
  const getPageTitle = () => {
    const segments = pathname.split("/").filter(Boolean)
    if (segments.length === 1 && segments[0] === "admin") return "Admin Dashboard"
    if (segments.length === 2) {
      const page = segments[1]
      return page.charAt(0).toUpperCase() + page.slice(1)
    }
    if (segments.length === 3) {
      const category = segments[1]
      const subcategory = segments[2]
      return `${category.charAt(0).toUpperCase() + category.slice(1)} - ${subcategory.charAt(0).toUpperCase() + subcategory.slice(1)}`
    }
    return "Admin Panel"
  }

  return (
    <header
      className="sticky top-0 z-30 flex h-16 items-center bg-[#001a2c] border-b border-[#003a4c] px-4"
      style={{ marginLeft: `${sidebarWidth}px` }}
    >
      <div className="flex flex-1 items-center justify-between">
        <div className="flex items-center space-x-3">
          <Shield className="h-6 w-6 text-red-400" />
          <h1 className="text-xl font-semibold text-white">{getPageTitle()}</h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Search users, orders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pl-10 bg-[#002a3c] border-[#003a4c] text-white focus:border-red-500"
            />
          </div>

          {/* Help */}
          <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
            <HelpCircle className="h-5 w-5" />
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative text-gray-400 hover:text-white">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white">
                  5
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 bg-[#002a3c] border-[#003a4c] text-white">
              <DropdownMenuLabel>Admin Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              {[
                {
                  title: "New KYC Submission",
                  description: "User John Doe submitted KYC documents for review.",
                  time: "5 minutes ago",
                  urgent: true,
                },
                {
                  title: "Withdrawal Request",
                  description: "User Jane Smith requested withdrawal of $2,500.",
                  time: "15 minutes ago",
                  urgent: true,
                },
                {
                  title: "Support Ticket",
                  description: "High priority support ticket #1234 needs attention.",
                  time: "1 hour ago",
                  urgent: false,
                },
              ].map((notification, index) => (
                <DropdownMenuItem
                  key={index}
                  className="flex flex-col items-start p-3 cursor-pointer hover:bg-[#003a4c]"
                >
                  <div className="flex justify-between w-full items-start">
                    <div className="font-medium">{notification.title}</div>
                    {notification.urgent && <Badge className="bg-red-500 text-white text-xs">Urgent</Badge>}
                  </div>
                  <div className="text-sm text-gray-400">{notification.description}</div>
                  <div className="text-xs text-gray-500 mt-1">{notification.time}</div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="justify-center text-red-400 hover:bg-[#003a4c] hover:text-red-300">
                View all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Admin Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-8 flex items-center space-x-2 text-gray-400 hover:text-white"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32" alt="Admin" />
                  <AvatarFallback className="bg-[#003a4c] text-white">AD</AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-white">Admin User</div>
                  <div className="text-xs text-red-400">Super Admin</div>
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-[#002a3c] border-[#003a4c] text-white">
              <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <Shield className="mr-2 h-4 w-4" />
                <span>Admin Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <Bell className="mr-2 h-4 w-4" />
                <span>Notifications</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-[#003a4c]" />
              <DropdownMenuItem className="hover:bg-[#003a4c]">
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
