"use client"

import { useEffect, useRef, useState } from "react"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"

export default function HeroSection() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sectionRef = useRef<HTMLElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)
  const [sectionHeight, setSectionHeight] = useState(0)

  // Set section height to viewport height
  useEffect(() => {
    const updateHeight = () => {
      setSectionHeight(window.innerHeight)
    }

    updateHeight()
    window.addEventListener("resize", updateHeight)

    return () => {
      window.removeEventListener("resize", updateHeight)
    }
  }, [])

  // Handle mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY,
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
    }
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      const { innerWidth, innerHeight } = window
      const dpr = window.devicePixelRatio || 1

      canvas.width = innerWidth * dpr
      canvas.height = innerHeight * dpr

      canvas.style.width = `${innerWidth}px`
      canvas.style.height = `${innerHeight}px`

      ctx.scale(dpr, dpr)
    }

    setCanvasDimensions()
    window.addEventListener("resize", setCanvasDimensions)

    // Bubble class for animation
    class Bubble {
      x: number
      y: number
      radius: number
      speed: number
      opacity: number
      color: string

      constructor() {
        this.x = (Math.random() * canvas.width) / window.devicePixelRatio
        this.y = canvas.height / window.devicePixelRatio + Math.random() * 100
        this.radius = Math.random() * 5 + 1
        this.speed = Math.random() * 1 + 0.5
        this.opacity = Math.random() * 0.5 + 0.1
        // Randomly choose between white and sky blue bubbles
        this.color = Math.random() > 0.7 ? "56, 189, 248" : "255, 255, 255" // Changed to sky blue
      }

      update() {
        this.y -= this.speed
        // Add slight horizontal movement
        this.x += Math.sin(this.y * 0.01) * 0.5

        // Reset bubble when it goes off screen
        if (this.y < -this.radius * 2) {
          this.x = (Math.random() * canvas.width) / window.devicePixelRatio
          this.y = canvas.height / window.devicePixelRatio + Math.random() * 20
          this.radius = Math.random() * 5 + 1
        }
      }

      draw() {
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(${this.color}, ${this.opacity})`
        ctx.fill()

        // Add subtle glow
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius * 1.5, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(${this.color}, ${this.opacity * 0.3})`
        ctx.fill()
      }
    }

    // Particle class for floating data points
    class DataParticle {
      x: number
      y: number
      size: number
      speed: number
      opacity: number
      type: string

      constructor() {
        this.x = (Math.random() * canvas.width) / window.devicePixelRatio
        this.y = (Math.random() * canvas.height) / window.devicePixelRatio
        this.size = Math.random() * 3 + 1
        this.speed = Math.random() * 0.5 + 0.2
        this.opacity = Math.random() * 0.3 + 0.1
        // Different types of data particles
        const types = ["$", "€", "¥", "£", "₿", "↑", "↓", "%"]
        this.type = types[Math.floor(Math.random() * types.length)]
      }

      update() {
        this.y -= this.speed
        this.x += Math.sin(this.y * 0.02) * 0.3

        // Reset particle when it goes off screen
        if (this.y < -10) {
          this.x = (Math.random() * canvas.width) / window.devicePixelRatio
          this.y = canvas.height / window.devicePixelRatio + Math.random() * 10
        }
      }

      draw() {
        ctx.font = `${this.size * 5}px monospace`
        ctx.fillStyle = `rgba(56, 189, 248, ${this.opacity})` // Changed to sky blue
        ctx.fillText(this.type, this.x, this.y)
      }
    }

    // Create bubbles and data particles
    const bubbles: Bubble[] = []
    for (let i = 0; i < 40; i++) {
      bubbles.push(new Bubble())
    }

    const dataParticles: DataParticle[] = []
    for (let i = 0; i < 15; i++) {
      dataParticles.push(new DataParticle())
    }

    // Create underwater effect
    const drawBackground = () => {
      // Create gradient for underwater effect
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height / window.devicePixelRatio)
      gradient.addColorStop(0, "rgba(0, 26, 44, 0.92)") // Dark blue at top
      gradient.addColorStop(1, "rgba(0, 10, 20, 0.95)") // Deeper blue at bottom

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)

      // Draw light rays that respond to mouse movement
      const rayCount = 8
      const centerX = mousePosition.x || canvas.width / (2 * window.devicePixelRatio)

      for (let i = 0; i < rayCount; i++) {
        const angle = (i / rayCount) * Math.PI * 2
        const rayX = centerX + Math.cos(angle) * 100
        const width = Math.random() * 100 + 50

        ctx.beginPath()
        ctx.moveTo(rayX, 0)
        ctx.lineTo(rayX + width / 2, canvas.height / window.devicePixelRatio)
        ctx.lineTo(rayX - width / 2, canvas.height / window.devicePixelRatio)
        ctx.closePath()

        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height / window.devicePixelRatio)
        gradient.addColorStop(0, "rgba(56, 189, 248, 0.1)") // Changed to sky blue
        gradient.addColorStop(1, "rgba(0, 26, 44, 0)")
        ctx.fillStyle = gradient
        ctx.fill()
      }

      // Create water ripple effect on mouse move
      if (mousePosition.x && mousePosition.y) {
        const rippleRadius = 50
        const rippleWidth = 2

        for (let i = 0; i < 3; i++) {
          const radius = rippleRadius - i * 10
          if (radius > 0) {
            ctx.beginPath()
            ctx.arc(mousePosition.x, mousePosition.y, radius, 0, Math.PI * 2)
            ctx.strokeStyle = `rgba(56, 189, 248, ${0.2 - i * 0.05})` // Changed to sky blue
            ctx.lineWidth = rippleWidth
            ctx.stroke()
          }
        }
      }

      // Update and draw bubbles
      bubbles.forEach((bubble) => {
        bubble.update()
        bubble.draw()
      })

      // Update and draw data particles
      dataParticles.forEach((particle) => {
        particle.update()
        particle.draw()
      })
    }

    // Animation loop
    let animationFrameId: number

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)
      drawBackground()
      animationFrameId = requestAnimationFrame(animate)
    }

    animate()

    // Cleanup
    return () => {
      window.removeEventListener("resize", setCanvasDimensions)
      cancelAnimationFrame(animationFrameId)
    }
  }, [mousePosition])

  // Text animation variants
  const titleVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
        staggerChildren: 0.1,
      },
    },
  }

  const letterVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  }

  const subtitleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 1.2,
        ease: "easeOut",
      },
    },
  }

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 1.5,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      boxShadow: "0px 0px 20px rgba(56, 189, 248, 0.6)", // Changed to sky blue
      transition: { duration: 0.3 },
    },
  }

  // Split text for letter animation
  const titleText = "Navigate the Financial Depths"
  const titleWords = titleText.split(" ")

  return (
    <section
      ref={sectionRef}
      className="relative overflow-hidden bg-[#001a2c]"
      style={{ height: `${sectionHeight}px` }}
    >
      {/* Background canvas */}
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full z-0" />

      {/* Whale image as background - more clearly visible now */}
      <div className="absolute inset-0 z-0 opacity-45 mix-blend-soft-light">
        <Image
          src="/images/whale-hero.png"
          alt=""
          fill
          className="object-cover object-center scale-110 brightness-125 contrast-110"
          priority
          aria-hidden="true"
          onLoad={() => setIsLoaded(true)}
        />
      </div>

      {/* Subtle overlay gradient to improve text readability */}
      <div className="absolute inset-0 z-0 bg-gradient-radial from-transparent to-[#001a2c]/60"></div>

      <div className="container mx-auto px-4 relative z-10 h-full flex flex-col justify-center items-center">
        <AnimatePresence>
          {isLoaded && (
            <motion.div initial="hidden" animate="visible" className="text-center max-w-4xl">
              <motion.h1
                variants={titleVariants}
                className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-white drop-shadow-md underwater-text"
              >
                {titleWords.map((word, i) => (
                  <span key={i} className="inline-block mr-4">
                    {word.split("").map((char, j) => (
                      <motion.span
                        key={`${i}-${j}`}
                        variants={letterVariants}
                        className={`inline-block ${
                          word === "Financial"
                            ? "text-transparent bg-clip-text bg-gradient-to-r from-sky-400 to-blue-400 glow-text" // Changed from teal to sky
                            : ""
                        }`}
                      >
                        {char}
                      </motion.span>
                    ))}
                  </span>
                ))}
              </motion.h1>

              <motion.p
                variants={subtitleVariants}
                className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto drop-shadow"
              >
                Join 72,000+ traders who trust our funded accounts to explore the markets with confidence
              </motion.p>

              <motion.div variants={buttonVariants} className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.div whileHover="hover">
                  <Button
                    size="lg"
                    className="bg-sky-500 hover:bg-sky-600 text-white px-8 py-6 text-lg shadow-lg transition-all duration-300 button-glow" // Changed from teal to sky
                  >
                    Dive In Now
                  </Button>
                </motion.div>
                <motion.div whileHover="hover">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-sky-500/30 text-white hover:bg-sky-500/10 px-8 py-6 text-lg transition-all duration-300" // Changed from teal to sky
                  >
                    Explore Accounts
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2, duration: 0.8 }}
          className="absolute bottom-10 left-0 right-0 flex justify-center"
        >
          <div className="flex flex-col items-center">
            <span className="text-gray-400 text-sm mb-2">Scroll to explore</span>
            <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center p-1">
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
                className="w-2 h-2 bg-sky-400 rounded-full" // Changed from teal to sky
              />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Subtle trading chart pattern overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#001a2c] to-transparent z-0"></div>
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-[url('/images/chart-pattern.png')] bg-repeat-x bg-bottom opacity-10 z-0 chart-flow"></div>
    </section>
  )
}
