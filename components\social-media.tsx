"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"

export default function SocialMedia() {
  const socialLinks = [
    {
      name: "Discord",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-7 h-7"
        >
          <path d="M9 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M15 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 3.025 -1.34 4.5 -1.5l.238 2.5" />
          <path d="M14.5 17c0 1 1.5 3 2 3c1.5 0 2.764 -1.667 3.5 -3c.736 -1.333 .476 -5.833 -1.5 -11.5c-1.457 -1.015 -3.248 -1.34 -4.5 -1.5l-.25 2.5" />
          <path d="M7 16.5c3.5 1 6.5 1 10 0" />
        </svg>
      ),
      url: "https://discord.gg/fundedwhales",
    },
    {
      name: "Facebook",
      icon: <Facebook className="w-7 h-7" />,
      url: "https://facebook.com/fundedwhales",
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-7 h-7" />,
      url: "https://instagram.com/fundedwhales",
    },
    {
      name: "Telegram",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-7 h-7"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      ),
      url: "https://t.me/fundedwhales",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    hover: {
      scale: 1.2,
      y: -5,
      color: "#40E0D0",
      transition: { duration: 0.3 },
    },
  }

  return (
    <div className="flex justify-center items-center py-6">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="flex space-x-6 p-3 rounded-full bg-[#002a3c]/80 backdrop-blur-sm border border-teal-500/20 shadow-lg"
      >
        <TooltipProvider>
          {socialLinks.map((social, index) => (
            <motion.div key={index} variants={itemVariants} whileHover="hover">
              <Tooltip>
                <TooltipTrigger asChild>
                  <a
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`Follow us on ${social.name}`}
                    className="flex items-center justify-center w-12 h-12 rounded-full bg-[#001a2c]/50 text-gray-300 hover:text-teal-400 hover:bg-teal-500/10 transition-all duration-300"
                  >
                    {social.icon}
                  </a>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Follow us on {social.name}</p>
                </TooltipContent>
              </Tooltip>
            </motion.div>
          ))}
        </TooltipProvider>
      </motion.div>
    </div>
  )
}
