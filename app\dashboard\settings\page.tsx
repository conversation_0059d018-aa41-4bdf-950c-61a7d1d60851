"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, Lock, Bell, Globe, CreditCard, Shield, LogOut, Upload, Trash, Eye, EyeOff } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default function SettingsPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Account Settings</CardTitle>
            <CardDescription className="text-gray-400">
              Manage your account preferences and personal information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="profile" className="w-full">
              <TabsList className="bg-[#001a2c]">
                <TabsTrigger value="profile" className="data-[state=active]:bg-teal-500 data-[state=active]:text-white">
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                >
                  <Lock className="h-4 w-4 mr-2" />
                  Security
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                >
                  <Bell className="h-4 w-4 mr-2" />
                  Notifications
                </TabsTrigger>
                <TabsTrigger
                  value="preferences"
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                >
                  <Globe className="h-4 w-4 mr-2" />
                  Preferences
                </TabsTrigger>
                <TabsTrigger value="billing" className="data-[state=active]:bg-teal-500 data-[state=active]:text-white">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Billing
                </TabsTrigger>
              </TabsList>

              <TabsContent value="profile" className="mt-6">
                <div className="space-y-8">
                  <div className="flex flex-col md:flex-row gap-8">
                    <div className="flex flex-col items-center space-y-4">
                      <Avatar className="h-24 w-24">
                        <AvatarImage src="/placeholder.svg?height=96&width=96" alt="User" />
                        <AvatarFallback className="bg-[#003a4c] text-white text-xl">JD</AvatarFallback>
                      </Avatar>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                          <Upload className="h-4 w-4 mr-2" />
                          Upload
                        </Button>
                        <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                          <Trash className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    </div>

                    <div className="flex-1 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="firstName" className="text-white">
                            First Name
                          </Label>
                          <Input
                            id="firstName"
                            defaultValue="John"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName" className="text-white">
                            Last Name
                          </Label>
                          <Input
                            id="lastName"
                            defaultValue="Doe"
                            className="bg-[#001a2c] border-[#003a4c] text-white"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-white">
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          defaultValue="<EMAIL>"
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-white">
                          Phone Number
                        </Label>
                        <Input
                          id="phone"
                          type="tel"
                          defaultValue="+****************"
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio" className="text-white">
                          Bio
                        </Label>
                        <textarea
                          id="bio"
                          rows={4}
                          className="w-full rounded-md bg-[#001a2c] border border-[#003a4c] text-white p-3 focus:outline-none focus:ring-2 focus:ring-teal-500"
                          placeholder="Tell us about yourself..."
                        ></textarea>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button className="bg-teal-500 hover:bg-teal-600 text-white">Save Changes</Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="security" className="mt-6">
                <div className="space-y-8">
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-white">Change Password</h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="currentPassword" className="text-white">
                          Current Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="currentPassword"
                            type={showCurrentPassword ? "text" : "password"}
                            className="bg-[#001a2c] border-[#003a4c] text-white pr-10"
                          />
                          <button
                            type="button"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                          >
                            {showCurrentPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="newPassword" className="text-white">
                          New Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            type={showPassword ? "text" : "password"}
                            className="bg-[#001a2c] border-[#003a4c] text-white pr-10"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                          >
                            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword" className="text-white">
                          Confirm New Password
                        </Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      </div>

                      <Button className="bg-teal-500 hover:bg-teal-600 text-white">Update Password</Button>
                    </div>
                  </div>

                  <div className="pt-6 border-t border-[#003a4c] space-y-6">
                    <h3 className="text-lg font-medium text-white">Two-Factor Authentication</h3>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white">Protect your account with 2FA</p>
                        <p className="text-sm text-gray-400">
                          Add an extra layer of security to your account by enabling two-factor authentication.
                        </p>
                      </div>
                      <Switch />
                    </div>
                    <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                      <Shield className="mr-2 h-4 w-4" />
                      Setup 2FA
                    </Button>
                  </div>

                  <div className="pt-6 border-t border-[#003a4c] space-y-6">
                    <h3 className="text-lg font-medium text-white">Sessions</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]">
                        <div>
                          <p className="text-white">Current Session</p>
                          <p className="text-sm text-gray-400">
                            Chrome on Windows • IP: *********** • Last active: Just now
                          </p>
                        </div>
                        <Badge className="bg-green-500/20 text-green-400">Active</Badge>
                      </div>
                      <div className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]">
                        <div>
                          <p className="text-white">Mobile App</p>
                          <p className="text-sm text-gray-400">
                            iPhone 13 • IP: *********** • Last active: 2 hours ago
                          </p>
                        </div>
                        <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                          Revoke
                        </Button>
                      </div>
                    </div>
                    <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                      <LogOut className="mr-2 h-4 w-4" />
                      Log Out All Devices
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="notifications" className="mt-6">
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-white">Email Notifications</h3>
                  <div className="space-y-4">
                    {[
                      {
                        title: "Account Updates",
                        description: "Receive emails about your account activity and security.",
                        defaultChecked: true,
                      },
                      {
                        title: "Trading Alerts",
                        description: "Get notified about important trading events and opportunities.",
                        defaultChecked: true,
                      },
                      {
                        title: "Challenge Updates",
                        description: "Receive updates about your trading challenge progress.",
                        defaultChecked: true,
                      },
                      {
                        title: "Withdrawal Notifications",
                        description: "Get notified when your withdrawals are processed.",
                        defaultChecked: true,
                      },
                      {
                        title: "Marketing & Promotions",
                        description: "Receive special offers, promotions, and news about our services.",
                        defaultChecked: false,
                      },
                    ].map((notification, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="text-white">{notification.title}</p>
                          <p className="text-sm text-gray-400">{notification.description}</p>
                        </div>
                        <Switch defaultChecked={notification.defaultChecked} />
                      </div>
                    ))}
                  </div>

                  <div className="pt-6 border-t border-[#003a4c] space-y-6">
                    <h3 className="text-lg font-medium text-white">Push Notifications</h3>
                    <div className="space-y-4">
                      {[
                        {
                          title: "Trading Platform Alerts",
                          description: "Receive alerts about price movements and trading signals.",
                          defaultChecked: true,
                        },
                        {
                          title: "Account Security",
                          description: "Get notified about login attempts and security alerts.",
                          defaultChecked: true,
                        },
                        {
                          title: "Challenge Milestones",
                          description: "Receive notifications when you reach important milestones.",
                          defaultChecked: true,
                        },
                      ].map((notification, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="text-white">{notification.title}</p>
                            <p className="text-sm text-gray-400">{notification.description}</p>
                          </div>
                          <Switch defaultChecked={notification.defaultChecked} />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button className="bg-teal-500 hover:bg-teal-600 text-white">Save Preferences</Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="preferences" className="mt-6">
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-white">Language & Region</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="language" className="text-white">
                        Language
                      </Label>
                      <select
                        id="language"
                        className="w-full h-10 px-3 rounded-md bg-[#001a2c] border border-[#003a4c] text-white focus:outline-none focus:ring-2 focus:ring-teal-500"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="zh">Chinese</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="region" className="text-white">
                        Region
                      </Label>
                      <select
                        id="region"
                        className="w-full h-10 px-3 rounded-md bg-[#001a2c] border border-[#003a4c] text-white focus:outline-none focus:ring-2 focus:ring-teal-500"
                      >
                        <option value="us">United States</option>
                        <option value="eu">Europe</option>
                        <option value="as">Asia</option>
                        <option value="af">Africa</option>
                        <option value="au">Australia</option>
                      </select>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="billing" className="mt-6">
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-white">Billing Information</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="billingAddress" className="text-white">
                        Billing Address
                      </Label>
                      <Input
                        id="billingAddress"
                        defaultValue="123 Main St, Anytown, USA"
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="paymentMethod" className="text-white">
                        Payment Method
                      </Label>
                      <Input
                        id="paymentMethod"
                        defaultValue="Credit Card"
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
