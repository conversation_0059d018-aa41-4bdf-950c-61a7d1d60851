"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap, Clock, Shield, BarChart2, Award, Percent, Star, Crown, Diamond } from "lucide-react"

export default function FeaturesSection() {
  const features = [
    {
      icon: <Zap className="h-12 w-12 text-sky-400" />,
      title: "HFT Friendly",
      description: "Designed specifically for high-frequency traders with ultra-low latency and lightning-fast execution.",
      premium: true,
    },
    {
      icon: <Clock className="h-12 w-12 text-sky-400" />,
      title: "Instant Verification",
      description: "Get verified and start trading within minutes, not days. Premium onboarding experience.",
      premium: false,
    },
    {
      icon: <Shield className="h-12 w-12 text-sky-400" />,
      title: "Secure Platform",
      description: "Bank-grade security with multi-layer encryption to protect your account and trading data.",
      premium: true,
    },
    {
      icon: <BarChart2 className="h-12 w-12 text-sky-400" />,
      title: "Advanced Analytics",
      description: "Professional-grade analytics suite with real-time performance insights and risk management tools.",
      premium: false,
    },
    {
      icon: <Award className="h-12 w-12 text-sky-400" />,
      title: "Elite Challenges",
      description: "Exclusive trading challenges designed for serious traders seeking substantial funding opportunities.",
      premium: true,
    },
    {
      icon: <Percent className="h-12 w-12 text-sky-400" />,
      title: "Premium Profit Split",
      description: "Industry-leading profit splits up to 90% with transparent fee structure and no hidden costs.",
      premium: false,
    },
  ]

  return (
    <section className="py-24 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100],
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 6,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-6">
            <Crown className="h-8 w-8 text-sky-400 mr-3" />
            <span className="text-sky-400 font-semibold tracking-wider uppercase text-sm">Premium Trading</span>
            <Crown className="h-8 w-8 text-sky-400 ml-3" />
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            Why Elite Traders Choose{" "}
            <span className="bg-gradient-to-r from-sky-400 via-blue-400 to-sky-500 bg-clip-text text-transparent">
              Funded Whales
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-sky-400 to-blue-400 mx-auto mb-6"></div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Experience the pinnacle of professional trading with our exclusive platform designed for serious traders who demand excellence.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="group"
            >
              <Card className="bg-gradient-to-br from-[#002a3c]/80 to-[#001e30]/80 border border-sky-500/20 h-full backdrop-blur-sm hover:border-sky-400/40 transition-all duration-500 relative overflow-hidden">
                {/* Premium badge */}
                {feature.premium && (
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center bg-gradient-to-r from-sky-500/20 to-blue-500/20 backdrop-blur-sm border border-sky-400/30 rounded-full px-3 py-1">
                      <Diamond className="h-3 w-3 text-sky-400 mr-1" />
                      <span className="text-xs text-sky-400 font-semibold">PREMIUM</span>
                    </div>
                  </div>
                )}

                {/* Luxury glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-sky-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Animated border */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-sky-400/20 via-blue-400/20 to-sky-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <CardHeader className="relative z-10 pb-4">
                  <div className="mb-6 relative">
                    <div className="w-20 h-20 bg-gradient-to-br from-sky-500/20 to-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-sky-400/30 group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    {/* Floating stars for premium features */}
                    {feature.premium && (
                      <div className="absolute -top-2 -right-2">
                        <Star className="h-4 w-4 text-sky-400 fill-sky-400/50" />
                      </div>
                    )}
                  </div>
                  <CardTitle className="text-xl font-bold text-white mb-3 group-hover:text-sky-100 transition-colors duration-300">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <CardDescription className="text-gray-300 leading-relaxed text-base group-hover:text-gray-200 transition-colors duration-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>

                {/* Luxury shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Luxury bottom accent */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center bg-gradient-to-r from-sky-500/10 to-blue-500/10 backdrop-blur-sm border border-sky-400/20 rounded-full px-8 py-4">
            <Diamond className="h-5 w-5 text-sky-400 mr-3" />
            <span className="text-sky-400 font-semibold">Join 10,000+ Elite Traders</span>
            <Diamond className="h-5 w-5 text-sky-400 ml-3" />
          </div>
        </motion.div>
      </div>
    </section>
  )
}
