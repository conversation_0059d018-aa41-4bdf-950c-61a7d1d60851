"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Users,
  DollarSign,
  TrendingUp,
  Activity,
  UserCheck,
  Clock,
  CheckCircle,
  Play,
  XCircle,
  BarChart3,
  Award,
  MessageSquare,
  Shield,
} from "lucide-react"

export default function AdminDashboard() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const stats = [
    {
      title: "Total Users",
      value: "2,847",
      change: "+12.5%",
      icon: Users,
      color: "text-sky-500",
      bgColor: "bg-sky-500/10",
      borderColor: "border-sky-500/20",
    },
    {
      title: "Active Orders",
      value: "1,234",
      change: "+8.2%",
      icon: Play,
      color: "text-blue-500",
      bgColor: "bg-blue-500/10",
      borderColor: "border-blue-500/20",
    },
    {
      title: "Total Revenue",
      value: "$847,392",
      change: "+23.1%",
      icon: DollarSign,
      color: "text-sky-500",
      bgColor: "bg-sky-500/10",
      borderColor: "border-sky-500/20",
    },
    {
      title: "Pending KYC",
      value: "89",
      change: "-5.4%",
      icon: Shield,
      color: "text-yellow-500",
      bgColor: "bg-yellow-500/10",
      borderColor: "border-yellow-500/20",
    },
  ]

  const orderStats = [
    { label: "Running", count: 456, icon: Play, color: "text-blue-500" },
    { label: "Completed", count: 1289, icon: CheckCircle, color: "text-sky-500" },
    { label: "Passed", count: 892, icon: TrendingUp, color: "text-blue-500" },
    { label: "Failed", count: 234, icon: XCircle, color: "text-red-500" },
    { label: "Stage 2", count: 167, icon: BarChart3, color: "text-purple-500" },
    { label: "Live", count: 89, icon: Clock, color: "text-yellow-500" },
  ]

  const recentActivities = [
    {
      id: 1,
      user: "John Doe",
      action: "Completed Phase 1 Challenge",
      time: "2 minutes ago",
      type: "success",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 2,
      user: "Jane Smith",
      action: "KYC Verification Submitted",
      time: "15 minutes ago",
      type: "pending",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 3,
      user: "Mike Johnson",
      action: "Withdrawal Request",
      time: "1 hour ago",
      type: "info",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    {
      id: 4,
      user: "Sarah Wilson",
      action: "Failed Challenge Attempt",
      time: "2 hours ago",
      type: "error",
      avatar: "/placeholder.svg?height=32&width=32",
    },
  ]

  const getActivityBadge = (type: string) => {
    switch (type) {
      case "success":
        return <Badge className="bg-sky-500/10 text-sky-500 border-sky-500/20">Success</Badge>
      case "pending":
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">Pending</Badge>
      case "info":
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">Info</Badge>
      case "error":
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Error</Badge>
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-gray-400">Welcome back! Here's what's happening with your platform.</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
              <Activity className="mr-2 h-4 w-4" />
              System Status
            </Button>
            <Button className="bg-sky-500 hover:bg-sky-600 text-white">
              <TrendingUp className="mr-2 h-4 w-4" />
              View Reports
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="bg-[#002a3c] border-[#003a4c]">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <p className={`text-sm ${stat.change.startsWith("+") ? "text-sky-500" : "text-red-500"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor} ${stat.borderColor} border`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Status Overview */}
        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Order Status Overview</CardTitle>
              <CardDescription className="text-gray-400">Current status of all trading orders</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {orderStats.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <stat.icon className={`h-5 w-5 ${stat.color}`} />
                      <span className="text-white font-medium">{stat.label}</span>
                    </div>
                    <Badge variant="secondary" className="bg-[#001a2c] text-white">
                      {stat.count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Recent Activity</CardTitle>
              <CardDescription className="text-gray-400">Latest user actions and system events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={activity.avatar || "/placeholder.svg"} alt={activity.user} />
                      <AvatarFallback className="bg-[#003a4c] text-white">
                        {activity.user
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-white">{activity.user}</p>
                      <p className="text-sm text-gray-400 truncate">{activity.action}</p>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      {getActivityBadge(activity.type)}
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
            <CardDescription className="text-gray-400">Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button className="h-20 flex-col bg-sky-500/10 hover:bg-sky-500/20 border border-sky-500/20 text-sky-500">
                <UserCheck className="h-6 w-6 mb-2" />
                <span className="text-sm">Verify KYC</span>
              </Button>
              <Button className="h-20 flex-col bg-blue-500/10 hover:bg-blue-500/20 border border-blue-500/20 text-blue-500">
                <Award className="h-6 w-6 mb-2" />
                <span className="text-sm">Issue Certificate</span>
              </Button>
              <Button className="h-20 flex-col bg-purple-500/10 hover:bg-purple-500/20 border border-purple-500/20 text-purple-500">
                <DollarSign className="h-6 w-6 mb-2" />
                <span className="text-sm">Process Withdrawal</span>
              </Button>
              <Button className="h-20 flex-col bg-yellow-500/10 hover:bg-yellow-500/20 border border-yellow-500/20 text-yellow-500">
                <MessageSquare className="h-6 w-6 mb-2" />
                <span className="text-sm">Support Tickets</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
