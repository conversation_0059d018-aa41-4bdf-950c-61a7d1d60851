"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import {
  BarChart3,
  ChevronDown,
  CreditCard,
  DollarSign,
  Download,
  LineChart,
  TrendingDown,
  TrendingUp,
  Users,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Bell,
} from "lucide-react"

export default function DashboardPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("7d")

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: "Account Balance",
            value: "$98,450",
            icon: <DollarSign className="h-5 w-5 text-sky-500" />,
            change: "+2.5%",
            trend: "up",
          },
          {
            title: "Total Profit",
            value: "$12,450",
            icon: <TrendingUp className="h-5 w-5 text-sky-500" />,
            change: "+15.3%",
            trend: "up",
          },
          {
            title: "Drawdown",
            value: "1.55%",
            icon: <TrendingDown className="h-5 w-5 text-red-500" />,
            change: "-0.3%",
            trend: "down",
          },
          {
            title: "Payout Available",
            value: "$9,960",
            icon: <CreditCard className="h-5 w-5 text-sky-500" />,
            change: "+$9,960",
            trend: "up",
          },
        ].map((stat, index) => (
          <motion.div
            key={index}
            variants={fadeInUp}
            initial="hidden"
            animate="visible"
            transition={{ delay: index * 0.1 }}
          >
            <Card className="bg-[#002a3c] border-[#003a4c]">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-gray-400">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <p className={`text-xs flex items-center ${stat.trend === "up" ? "text-sky-500" : "text-red-500"}`}>
                  {stat.trend === "up" ? (
                    <TrendingUp className="mr-1 h-3 w-3" />
                  ) : (
                    <TrendingDown className="mr-1 h-3 w-3" />
                  )}
                  {stat.change} from last period
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Account Status */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Account Status</CardTitle>
            <CardDescription className="text-gray-400">Your current trading challenge progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Profit Target (8%)</span>
                  <span className="text-sm font-medium text-white">4.2% / 8%</span>
                </div>
                <Progress value={52.5} className="h-2 bg-gray-700">
                  <div className="h-full bg-sky-500" style={{ width: "52.5%" }} />
                </Progress>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Maximum Drawdown (5%)</span>
                  <span className="text-sm font-medium text-white">1.55% / 5%</span>
                </div>
                <Progress value={31} className="h-2 bg-gray-700">
                  <div className="h-full bg-sky-400" style={{ width: "31%" }} />
                </Progress>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Minimum Trading Days</span>
                  <span className="text-sm font-medium text-white">8 / 10 days</span>
                </div>
                <Progress value={80} className="h-2 bg-gray-700">
                  <div className="h-full bg-blue-500" style={{ width: "80%" }} />
                </Progress>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-[#001a2c] rounded-lg p-4 border border-[#003a4c]">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-5 w-5 text-sky-500" />
                    <h3 className="text-sm font-medium text-white">Time Remaining</h3>
                  </div>
                  <p className="text-2xl font-bold text-white">12 days</p>
                  <p className="text-xs text-gray-400 mt-1">No time pressure, take your time</p>
                </div>

                <div className="bg-[#001a2c] rounded-lg p-4 border border-[#003a4c]">
                  <div className="flex items-center space-x-2 mb-2">
                    <Calendar className="h-5 w-5 text-sky-500" />
                    <h3 className="text-sm font-medium text-white">Trading Days</h3>
                  </div>
                  <p className="text-2xl font-bold text-white">8 days</p>
                  <p className="text-xs text-gray-400 mt-1">2 more days required</p>
                </div>

                <div className="bg-[#001a2c] rounded-lg p-4 border border-[#003a4c]">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                    <h3 className="text-sm font-medium text-white">Account Status</h3>
                  </div>
                  <p className="text-2xl font-bold text-white">In Progress</p>
                  <p className="text-xs text-gray-400 mt-1">Challenge Phase 1</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Trading Performance & Recent Trades */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.5 }}>
        <Tabs defaultValue="performance" className="w-full">
          <div className="flex justify-between items-center mb-4">
            <TabsList className="bg-[#001a2c]">
              <TabsTrigger
                value="performance"
                className="data-[state=active]:bg-sky-500 data-[state=active]:text-white"
              >
                Performance
              </TabsTrigger>
              <TabsTrigger value="trades" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
                Trades
              </TabsTrigger>
              <TabsTrigger
                value="statistics"
                className="data-[state=active]:bg-sky-500 data-[state=active]:text-white"
              >
                Statistics
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                {selectedPeriod === "7d" ? "Last 7 Days" : selectedPeriod === "30d" ? "Last 30 Days" : "All Time"}
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <TabsContent value="performance">
            <Card className="bg-[#002a3c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Performance Overview</CardTitle>
                <CardDescription className="text-gray-400">Your trading performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <LineChart className="h-16 w-16 text-gray-500" />
                  <p className="ml-4 text-gray-500">Performance chart visualization would appear here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trades">
            <Card className="bg-[#002a3c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Recent Trades</CardTitle>
                <CardDescription className="text-gray-400">Your most recent trading activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border border-[#003a4c]">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead className="border-b border-[#003a4c]">
                        <tr className="border-b border-[#003a4c]">
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Pair</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Type</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Size</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Entry</th>
                          <th className="h-12 px-4 text-left font-medium text-gray-400">Close</th>
                          <th className="h-12 px-4 text-right font-medium text-gray-400">P/L</th>
                        </tr>
                      </thead>
                      <tbody>
                        {[
                          {
                            pair: "EUR/USD",
                            type: "Buy",
                            size: "0.5 lot",
                            entry: "1.0921",
                            close: "1.0968",
                            pl: "+$235.00",
                            profit: true,
                          },
                          {
                            pair: "GBP/JPY",
                            type: "Sell",
                            size: "0.3 lot",
                            entry: "186.42",
                            close: "185.78",
                            pl: "+$180.50",
                            profit: true,
                          },
                          {
                            pair: "USD/CAD",
                            type: "Buy",
                            size: "0.2 lot",
                            entry: "1.3642",
                            close: "1.3598",
                            pl: "-$88.00",
                            profit: false,
                          },
                          {
                            pair: "XAU/USD",
                            type: "Buy",
                            size: "0.1 lot",
                            entry: "2305.45",
                            close: "2328.30",
                            pl: "+$228.50",
                            profit: true,
                          },
                          {
                            pair: "AUD/USD",
                            type: "Sell",
                            size: "0.4 lot",
                            entry: "0.6584",
                            close: "0.6612",
                            pl: "-$112.00",
                            profit: false,
                          },
                        ].map((trade, i) => (
                          <tr key={i} className="border-b border-[#003a4c]">
                            <td className="p-4 font-medium text-white">{trade.pair}</td>
                            <td className="p-4 text-gray-300">{trade.type}</td>
                            <td className="p-4 text-gray-300">{trade.size}</td>
                            <td className="p-4 text-gray-300">{trade.entry}</td>
                            <td className="p-4 text-gray-300">{trade.close}</td>
                            <td
                              className={`p-4 text-right font-medium ${
                                trade.profit ? "text-sky-500" : "text-red-500"
                              }`}
                            >
                              {trade.pl}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="statistics">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-[#002a3c] border-[#003a4c]">
                <CardHeader>
                  <CardTitle className="text-white">Win/Loss Ratio</CardTitle>
                  <CardDescription className="text-gray-400">Your trading success rate</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center">
                    <div className="relative w-32 h-32">
                      <div className="absolute inset-0 rounded-full bg-gray-700"></div>
                      <div
                        className="absolute inset-0 rounded-full bg-sky-500"
                        style={{ clipPath: "polygon(0 0, 68% 0, 68% 100%, 0 100%)" }}
                      ></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-2xl font-bold text-white">68%</span>
                      </div>
                    </div>
                    <div className="ml-8">
                      <div className="flex items-center mb-2">
                        <div className="w-3 h-3 rounded-full bg-sky-500 mr-2"></div>
                        <span className="text-gray-300">Win rate: 68%</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-gray-700 mr-2"></div>
                        <span className="text-gray-300">Loss rate: 32%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-[#002a3c] border-[#003a4c]">
                <CardHeader>
                  <CardTitle className="text-white">Trading Instruments</CardTitle>
                  <CardDescription className="text-gray-400">Distribution of your trades by instrument</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-center justify-center">
                    <BarChart3 className="h-16 w-16 text-gray-500" />
                    <p className="ml-4 text-gray-500">Instrument distribution chart would appear here</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Notifications & Account Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          className="lg:col-span-2"
          variants={fadeInUp}
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.6 }}
        >
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-white">Notifications</CardTitle>
                <CardDescription className="text-gray-400">Your recent notifications and alerts</CardDescription>
              </div>
              <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                Mark all as read
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    icon: <CheckCircle className="h-5 w-5 text-sky-500" />,
                    title: "KYC Verification Approved",
                    description: "Your identity verification has been successfully completed.",
                    time: "2 hours ago",
                    read: false,
                  },
                  {
                    icon: <DollarSign className="h-5 w-5 text-sky-500" />,
                    title: "Withdrawal Processed",
                    description: "Your withdrawal request for $1,250 has been processed and sent to your bank account.",
                    time: "Yesterday",
                    read: false,
                  },
                  {
                    icon: <Bell className="h-5 w-5 text-yellow-500" />,
                    title: "New Challenge Available",
                    description: "Check out our new HFT challenge with improved conditions and higher profit splits.",
                    time: "3 days ago",
                    read: true,
                  },
                  {
                    icon: <Users className="h-5 w-5 text-blue-500" />,
                    title: "Referral Bonus",
                    description: "You've earned a $50 referral bonus! Your friend John Smith has signed up.",
                    time: "1 week ago",
                    read: true,
                  },
                ].map((notification, i) => (
                  <div
                    key={i}
                    className={`flex p-3 rounded-lg border ${
                      notification.read ? "border-[#003a4c] bg-[#002a3c]" : "border-sky-500/30 bg-[#001a2c]"
                    }`}
                  >
                    <div className="mr-4 mt-1">{notification.icon}</div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium text-white">{notification.title}</h4>
                        <span className="text-xs text-gray-500">{notification.time}</span>
                      </div>
                      <p className="text-sm text-gray-400 mt-1">{notification.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="link" className="mt-4 text-sky-400 hover:text-sky-300 p-0">
                View all notifications
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.7 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Account Details</CardTitle>
              <CardDescription className="text-gray-400">Your trading account information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Account Type</span>
                  <span className="font-medium text-white">Standard $100K</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Account ID</span>
                  <span className="font-medium text-white">FW-********</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Platform</span>
                  <span className="font-medium text-white">MetaTrader 5</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Leverage</span>
                  <span className="font-medium text-white">1:100</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Profit Split</span>
                  <span className="font-medium text-white">80%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Next Payout</span>
                  <span className="font-medium text-white">May 15, 2025</span>
                </div>
              </div>

              <div className="mt-6">
                <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                  <Users className="mr-2 h-4 w-4" />
                  Contact Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
