"use client"

import { motion } from "framer-motion"
import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function FaqSection() {
  const faqs = [
    {
      question: "What is a trading challenge?",
      answer:
        "A trading challenge is an evaluation process that tests your trading skills and risk management. After passing the challenge, you gain access to a funded account where you can earn a percentage of the profits you generate without risking your own capital.",
    },
    {
      question: "How does the evaluation process work?",
      answer:
        "Our evaluation process consists of different options. For Step 1 and Step 2, you need to reach profit targets while adhering to our risk management rules. For HFT Neo, the process is optimized for high-frequency traders. With Instant accounts, you can skip the evaluation entirely and start trading with our capital immediately.",
    },
    {
      question: "What trading platforms do you support?",
      answer:
        "We support MetaTrader 4 (MT4), MetaTrader 5 (MT5), and cTrader platforms. You can choose your preferred platform during the registration process.",
    },
    {
      question: "How and when do I receive my profit payouts?",
      answer:
        "Profit payouts are processed on a bi-weekly basis. Once you request a withdrawal, the funds will be sent to your designated payment method within 24 hours. You'll receive your agreed profit split percentage.",
    },
    {
      question: "Are there any restrictions on trading styles or strategies?",
      answer:
        "We allow most trading styles and strategies, including scalping, day trading, swing trading, and position trading. However, we prohibit certain high-risk practices such as martingale strategies, excessive risk per trade, and arbitrage.",
    },
    {
      question: "Can I trade from any country?",
      answer:
        "Yes, our program is available worldwide. However, due to regulatory restrictions, traders from certain countries may need to provide additional verification.",
    },
  ]

  return (
    <section className="py-20 bg-[#001a2c]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Frequently Asked Questions</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Find answers to common questions about our trading challenges
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-3xl mx-auto"
        >
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="bg-[#002a3c] border border-[#003a4c] rounded-lg px-6"
              >
                <AccordionTrigger className="text-white hover:text-sky-400 text-left py-4">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-400 pb-4">{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  )
}
