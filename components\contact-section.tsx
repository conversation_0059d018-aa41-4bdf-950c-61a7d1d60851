"use client"

import type React from "react"

import { motion } from "framer-motion"
import { Mail, Clock, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useState } from "react"

export default function ContactSection() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    message: "",
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  }

  return (
    <section className="bg-[#001525] py-20 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-400 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-sky-400 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
        >
          {/* Left Side - Contact Info */}
          <motion.div variants={itemVariants} className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 underwater-text">Contact Us</h2>
              <div className="w-20 h-1 bg-gradient-to-r from-sky-400 to-blue-400 mb-6"></div>
              <p className="text-xl text-gray-300 mb-8">Get in touch with the team.</p>
            </div>

            <div className="space-y-6">
              {/* Email */}
              <motion.div
                variants={itemVariants}
                className="flex items-center space-x-4 p-4 rounded-lg bg-[#002a3c]/50 border border-[#003a4c] hover:border-sky-400/30 transition-all duration-300"
              >
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-sky-400/10 border border-sky-400/20">
                  <Mail className="w-6 h-6 text-sky-400" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Email us at</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-white text-lg font-medium hover:text-sky-400 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
              </motion.div>

              {/* Support Hours */}
              <motion.div
                variants={itemVariants}
                className="flex items-center space-x-4 p-4 rounded-lg bg-[#002a3c]/50 border border-[#003a4c] hover:border-sky-400/30 transition-all duration-300"
              >
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-sky-400/10 border border-sky-400/20">
                  <Clock className="w-6 h-6 text-sky-400" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Support Hours</p>
                  <p className="text-white text-lg font-medium">Support available 24/7</p>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Side - Contact Form */}
          <motion.div variants={itemVariants}>
            <div className="bg-[#002a3c]/30 backdrop-blur-sm border border-[#003a4c] rounded-2xl p-8 shadow-2xl">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Full Name */}
                <div>
                  <label htmlFor="fullName" className="block text-white text-sm font-medium mb-2">
                    Full Name
                  </label>
                  <Input
                    id="fullName"
                    name="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    className="bg-[#001525] border-[#003a4c] text-white placeholder:text-gray-500 focus:border-sky-400 focus:ring-sky-400/20 h-12"
                    required
                  />
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-white text-sm font-medium mb-2">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    className="bg-[#001525] border-[#003a4c] text-white placeholder:text-gray-500 focus:border-sky-400 focus:ring-sky-400/20 h-12"
                    required
                  />
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-white text-sm font-medium mb-2">
                    Phone Number
                  </label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                    className="bg-[#001525] border-[#003a4c] text-white placeholder:text-gray-500 focus:border-sky-400 focus:ring-sky-400/20 h-12"
                  />
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-white text-sm font-medium mb-2">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Tell us how we can help you..."
                    rows={5}
                    className="bg-[#001525] border-[#003a4c] text-white placeholder:text-gray-500 focus:border-sky-400 focus:ring-sky-400/20 resize-none"
                    required
                  />
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-sky-400 to-blue-400 hover:from-sky-500 hover:to-blue-500 text-[#001525] font-semibold h-12 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-sky-400/25"
                >
                  <Send className="w-5 h-5 mr-2" />
                  Send Message
                </Button>
              </form>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
