"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  ChevronRight,
  Clock,
  Download,
  ExternalLink,
  Eye,
  FileText,
  Lock,
  Plus,
  Settings,
  ShieldCheck,
  TrendingUp,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function AccountsPage() {
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const accounts = [
    {
      id: "acc1",
      name: "Standard $100K",
      balance: "$98,450",
      profit: "+$4,200",
      profitPercentage: "4.2%",
      drawdown: "1.55%",
      status: "active",
      phase: "Challenge Phase 1",
      platform: "MetaTrader 5",
      login: "FW78542196",
      password: "••••••••",
      server: "FundedWhales-Live1",
      progress: 52.5,
    },
    {
      id: "acc2",
      name: "HFT Neo $50K",
      balance: "$51,250",
      profit: "+$2,800",
      profitPercentage: "5.6%",
      drawdown: "0.8%",
      status: "active",
      phase: "Challenge Phase 2",
      platform: "MetaTrader 5",
      login: "FW78542197",
      password: "••••••••",
      server: "FundedWhales-Live2",
      progress: 70,
    },
    {
      id: "acc3",
      name: "Standard $25K",
      balance: "$0",
      profit: "$0",
      profitPercentage: "0%",
      drawdown: "0%",
      status: "expired",
      phase: "Challenge Phase 1",
      platform: "MetaTrader 5",
      login: "FW78542198",
      password: "••••••••",
      server: "FundedWhales-Live1",
      progress: 0,
    },
  ]

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Trading Accounts</h2>
        <Button className="bg-sky-500 hover:bg-sky-600 text-white">
          <Plus className="mr-2 h-4 w-4" />
          New Challenge
        </Button>
      </div>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="bg-[#001a2c]">
          <TabsTrigger value="active" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Active Accounts
          </TabsTrigger>
          <TabsTrigger value="funded" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Funded Accounts
          </TabsTrigger>
          <TabsTrigger value="expired" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Expired Accounts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="grid grid-cols-1 gap-6">
            {accounts
              .filter((account) => account.status === "active")
              .map((account, index) => (
                <motion.div
                  key={account.id}
                  variants={fadeInUp}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    className={`bg-[#002a3c] border-[#003a4c] cursor-pointer transition-all duration-200 hover:border-sky-500/50 ${
                      selectedAccount === account.id ? "border-sky-500" : ""
                    }`}
                    onClick={() => setSelectedAccount(selectedAccount === account.id ? null : account.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white">{account.name}</CardTitle>
                          <CardDescription className="text-gray-400">{account.phase}</CardDescription>
                        </div>
                        <Badge
                          className={`${
                            account.status === "active"
                              ? "bg-green-500/20 text-green-400"
                              : account.status === "funded"
                                ? "bg-sky-500/20 text-sky-400"
                                : "bg-gray-500/20 text-gray-400"
                          }`}
                        >
                          {account.status === "active" ? "Active" : account.status === "funded" ? "Funded" : "Expired"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-400">Balance</p>
                          <p className="text-xl font-bold text-white">{account.balance}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit</p>
                          <p className="text-xl font-bold text-sky-500">{account.profit}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit Target (8%)</p>
                          <div className="flex items-center">
                            <p className="text-xl font-bold text-white mr-2">{account.profitPercentage}</p>
                            <Progress value={account.progress} className="h-2 w-20 bg-gray-700">
                              <div className="h-full bg-sky-500" style={{ width: `${account.progress}%` }} />
                            </Progress>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Max Drawdown</p>
                          <p className="text-xl font-bold text-white">{account.drawdown}</p>
                        </div>
                      </div>

                      {selectedAccount === account.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4 pt-4 border-t border-[#003a4c]"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                              <div>
                                <h3 className="text-white font-medium mb-2">Account Details</h3>
                                <div className="space-y-2">
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Platform</span>
                                    <span className="text-white">{account.platform}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Login ID</span>
                                    <span className="text-white">{account.login}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Password</span>
                                    <div className="flex items-center">
                                      <span className="text-white mr-2">{account.password}</span>
                                      <Button variant="ghost" size="icon" className="h-5 w-5 text-gray-400">
                                        <Eye className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Server</span>
                                    <span className="text-white">{account.server}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                                >
                                  <Download className="mr-2 h-4 w-4" />
                                  Download MT5
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                                >
                                  <FileText className="mr-2 h-4 w-4" />
                                  Trading Rules
                                </Button>
                              </div>
                            </div>

                            <div className="space-y-4">
                              <div>
                                <h3 className="text-white font-medium mb-2">Challenge Progress</h3>
                                <div className="space-y-3">
                                  <div className="space-y-2">
                                    <div className="flex justify-between">
                                      <span className="text-sm text-gray-400">Profit Target (8%)</span>
                                      <span className="text-sm font-medium text-white">
                                        {account.profitPercentage} / 8%
                                      </span>
                                    </div>
                                    <Progress value={account.progress} className="h-2 bg-gray-700">
                                      <div className="h-full bg-sky-500" style={{ width: `${account.progress}%` }} />
                                    </Progress>
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex justify-between">
                                      <span className="text-sm text-gray-400">Maximum Drawdown (5%)</span>
                                      <span className="text-sm font-medium text-white">{account.drawdown} / 5%</span>
                                    </div>
                                    <Progress
                                      value={(Number.parseFloat(account.drawdown.replace("%", "")) / 5) * 100}
                                      className="h-2 bg-gray-700"
                                    >
                                      <div
                                        className="h-full bg-sky-400"
                                        style={{
                                          width: `${(Number.parseFloat(account.drawdown.replace("%", "")) / 5) * 100}%`,
                                        }}
                                      />
                                    </Progress>
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex justify-between">
                                      <span className="text-sm text-gray-400">Minimum Trading Days</span>
                                      <span className="text-sm font-medium text-white">8 / 10 days</span>
                                    </div>
                                    <Progress value={80} className="h-2 bg-gray-700">
                                      <div className="h-full bg-blue-500" style={{ width: "80%" }} />
                                    </Progress>
                                  </div>
                                </div>
                              </div>

                              <div className="flex space-x-2">
                                <Button className="bg-sky-500 hover:bg-sky-600 text-white">
                                  <BarChart3 className="mr-2 h-4 w-4" />
                                  View Statistics
                                </Button>
                                <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                                  <Settings className="mr-2 h-4 w-4" />
                                  Account Settings
                                </Button>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0 flex justify-end">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-sky-400 hover:text-sky-300"
                        onClick={(e) => {
                          e.stopPropagation()
                          setSelectedAccount(selectedAccount === account.id ? null : account.id)
                        }}
                      >
                        {selectedAccount === account.id ? "Hide Details" : "View Details"}
                        <ChevronRight
                          className={`ml-1 h-4 w-4 transition-transform ${
                            selectedAccount === account.id ? "rotate-90" : ""
                          }`}
                        />
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="funded">
          <div className="flex flex-col items-center justify-center py-12 px-4 border border-dashed border-[#003a4c] rounded-lg bg-[#001a2c]">
            <TrendingUp className="h-12 w-12 text-gray-500 mb-4" />
            <h3 className="text-xl font-medium text-white mb-2">No Funded Accounts Yet</h3>
            <p className="text-gray-400 text-center max-w-md mb-6">
              Complete your challenge successfully to receive a funded account with our capital.
            </p>
            <Button className="bg-sky-500 hover:bg-sky-600 text-white">
              <ExternalLink className="mr-2 h-4 w-4" />
              View Challenge Progress
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="expired">
          <div className="grid grid-cols-1 gap-6">
            {accounts
              .filter((account) => account.status === "expired")
              .map((account, index) => (
                <motion.div
                  key={account.id}
                  variants={fadeInUp}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-[#002a3c] border-[#003a4c] opacity-70">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white">{account.name}</CardTitle>
                          <CardDescription className="text-gray-400">{account.phase}</CardDescription>
                        </div>
                        <Badge className="bg-gray-500/20 text-gray-400">Expired</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-400">Balance</p>
                          <p className="text-xl font-bold text-white">{account.balance}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit</p>
                          <p className="text-xl font-bold text-white">{account.profit}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit Target (8%)</p>
                          <p className="text-xl font-bold text-white">{account.profitPercentage}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Max Drawdown</p>
                          <p className="text-xl font-bold text-white">{account.drawdown}</p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-0 flex justify-end">
                      <Button className="bg-sky-500 hover:bg-sky-600 text-white">
                        <Plus className="mr-2 h-4 w-4" />
                        Retry Challenge
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
          </div>
        </TabsContent>
      </Tabs>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Available Challenges</CardTitle>
            <CardDescription className="text-gray-400">
              Choose from our range of trading challenges to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  name: "Standard Challenge",
                  description: "Our classic two-phase evaluation process",
                  features: [
                    "Account sizes from $10K to $200K",
                    "8% profit target in Phase 1",
                    "5% profit target in Phase 2",
                    "5% maximum drawdown",
                    "80% profit split",
                  ],
                  price: "Starting at $99",
                },
                {
                  name: "HFT Neo Challenge",
                  description: "Optimized for high-frequency traders",
                  features: [
                    "Account sizes from $10K to $200K",
                    "5% profit target",
                    "3% maximum drawdown",
                    "90% profit split",
                    "Low latency execution",
                  ],
                  price: "Starting at $149",
                  featured: true,
                },
                {
                  name: "Instant Funding",
                  description: "Skip the evaluation and get funded immediately",
                  features: [
                    "Account sizes from $10K to $200K",
                    "No profit target",
                    "5% maximum drawdown",
                    "70% profit split",
                    "Start trading immediately",
                  ],
                  price: "Starting at $299",
                },
              ].map((challenge, index) => (
                <Card
                  key={index}
                  className={`bg-[#001a2c] border-[#003a4c] ${
                    challenge.featured ? "border-sky-500 border-2" : ""
                  } h-full`}
                >
                  <CardHeader>
                    <CardTitle className="text-white">{challenge.name}</CardTitle>
                    <CardDescription className="text-gray-400">{challenge.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-6">
                      {challenge.features.map((feature, i) => (
                        <li key={i} className="flex items-start text-gray-300">
                          <ShieldCheck className="h-5 w-5 text-sky-500 mr-2 shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <p className="text-sky-400 font-medium">{challenge.price}</p>
                  </CardContent>
                  <CardFooter>
                    <Button
                      className={
                        challenge.featured
                          ? "w-full bg-sky-500 hover:bg-sky-600 text-white"
                          : "w-full bg-[#003a4c] hover:bg-[#004a5c] text-white"
                      }
                    >
                      Get Started
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Account Security</CardTitle>
            <CardDescription className="text-gray-400">
              Ensure your trading accounts remain secure and protected
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col items-center p-6 bg-[#001a2c] rounded-lg border border-[#003a4c]">
                <Lock className="h-10 w-10 text-sky-500 mb-4" />
                <h3 className="text-white font-medium mb-2">Change Password</h3>
                <p className="text-gray-400 text-center mb-4">
                  Regularly update your trading platform password for enhanced security.
                </p>
                <Button variant="outline" className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c]">
                  Update Password
                </Button>
              </div>

              <div className="flex flex-col items-center p-6 bg-[#001a2c] rounded-lg border border-[#003a4c]">
                <ShieldCheck className="h-10 w-10 text-sky-500 mb-4" />
                <h3 className="text-white font-medium mb-2">Two-Factor Authentication</h3>
                <p className="text-gray-400 text-center mb-4">
                  Add an extra layer of security to your account with 2FA.
                </p>
                <Button variant="outline" className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c]">
                  Enable 2FA
                </Button>
              </div>

              <div className="flex flex-col items-center p-6 bg-[#001a2c] rounded-lg border border-[#003a4c]">
                <Clock className="h-10 w-10 text-sky-500 mb-4" />
                <h3 className="text-white font-medium mb-2">Login History</h3>
                <p className="text-gray-400 text-center mb-4">
                  Monitor recent login activity to detect unauthorized access.
                </p>
                <Button variant="outline" className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c]">
                  View History
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
