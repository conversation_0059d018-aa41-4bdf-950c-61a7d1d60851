"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  MessageSquare,
  Phone,
  Mail,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  ChevronRight,
  HelpCircle,
  AlertCircle,
} from "lucide-react"

export default function SupportPage() {
  const [subject, setSubject] = useState("")
  const [message, setMessage] = useState("")
  const [searchQuery, setSearchQuery] = useState("")

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const tickets = [
    {
      id: "TKT-001",
      subject: "Account Verification Issue",
      date: "May 5, 2025",
      status: "open",
      priority: "high",
      lastUpdate: "2 hours ago",
    },
    {
      id: "TKT-002",
      subject: "Trading Platform Connection Problem",
      date: "May 3, 2025",
      status: "in-progress",
      priority: "medium",
      lastUpdate: "1 day ago",
    },
    {
      id: "TKT-003",
      subject: "Withdrawal Request Clarification",
      date: "Apr 28, 2025",
      status: "closed",
      priority: "low",
      lastUpdate: "1 week ago",
    },
  ]

  const faqs = [
    {
      question: "How long does it take to get a response from support?",
      answer:
        "We typically respond to all support inquiries within 24 hours during business days. High priority tickets may receive faster responses.",
    },
    {
      question: "How do I reset my trading platform password?",
      answer:
        "You can reset your trading platform password by going to the Account Settings page in your dashboard and selecting the 'Reset Password' option under the Security section.",
    },
    {
      question: "What should I do if I can't connect to the trading platform?",
      answer:
        "First, check your internet connection. Then, verify that you're using the correct server, login, and password. If the issue persists, try reinstalling the platform or contact our support team for assistance.",
    },
    {
      question: "How do I withdraw my profits?",
      answer:
        "You can request a withdrawal from the Withdrawals section of your dashboard. Withdrawals are processed within 24 hours and sent to your registered payment method.",
    },
    {
      question: "What happens if I violate a trading rule?",
      answer:
        "Minor violations may result in warnings. Repeated or severe violations may result in account termination. Please review our trading rules carefully to avoid any issues.",
    },
  ]

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div className="md:col-span-2" variants={fadeInUp} initial="hidden" animate="visible">
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Support Center</CardTitle>
              <CardDescription className="text-gray-400">
                Get help with your account, trading platform, or any other questions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="tickets" className="w-full">
                <TabsList className="bg-[#001a2c]">
                  <TabsTrigger
                    value="tickets"
                    className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                  >
                    My Tickets
                  </TabsTrigger>
                  <TabsTrigger
                    value="new-ticket"
                    className="data-[state=active]:bg-teal-500 data-[state=active]:text-white"
                  >
                    New Ticket
                  </TabsTrigger>
                  <TabsTrigger value="faq" className="data-[state=active]:bg-teal-500 data-[state=active]:text-white">
                    FAQ
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="tickets" className="mt-6">
                  <div className="space-y-4">
                    {tickets.map((ticket) => (
                      <div
                        key={ticket.id}
                        className="flex flex-col md:flex-row md:items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]"
                      >
                        <div className="flex items-start mb-4 md:mb-0">
                          <div
                            className={`h-10 w-10 rounded-full flex items-center justify-center mr-4 ${
                              ticket.status === "open"
                                ? "bg-yellow-500/20"
                                : ticket.status === "in-progress"
                                  ? "bg-blue-500/20"
                                  : "bg-green-500/20"
                            }`}
                          >
                            {ticket.status === "open" ? (
                              <Clock className="h-5 w-5 text-yellow-500" />
                            ) : ticket.status === "in-progress" ? (
                              <MessageSquare className="h-5 w-5 text-blue-500" />
                            ) : (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                          </div>
                          <div>
                            <div className="flex items-center">
                              <p className="text-white font-medium">{ticket.subject}</p>
                              <Badge
                                className={`ml-2 ${
                                  ticket.priority === "high"
                                    ? "bg-red-500/20 text-red-400"
                                    : ticket.priority === "medium"
                                      ? "bg-yellow-500/20 text-yellow-400"
                                      : "bg-blue-500/20 text-blue-400"
                                }`}
                              >
                                {ticket.priority}
                              </Badge>
                            </div>
                            <div className="flex items-center text-sm text-gray-400 mt-1">
                              <span className="mr-3">#{ticket.id}</span>
                              <span className="mr-3">Created: {ticket.date}</span>
                              <span>Last update: {ticket.lastUpdate}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Badge
                            className={`mr-4 ${
                              ticket.status === "open"
                                ? "bg-yellow-500/20 text-yellow-400"
                                : ticket.status === "in-progress"
                                  ? "bg-blue-500/20 text-blue-400"
                                  : "bg-green-500/20 text-green-400"
                            }`}
                          >
                            {ticket.status === "open"
                              ? "Open"
                              : ticket.status === "in-progress"
                                ? "In Progress"
                                : "Closed"}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="new-ticket" className="mt-6">
                  <form className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="subject" className="text-white">
                        Subject
                      </Label>
                      <Input
                        id="subject"
                        placeholder="Brief description of your issue"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="category" className="text-white">
                          Category
                        </Label>
                        <select
                          id="category"
                          className="w-full h-10 px-3 rounded-md bg-[#001a2c] border border-[#003a4c] text-white focus:outline-none focus:ring-2 focus:ring-teal-500"
                          required
                        >
                          <option value="">Select a category</option>
                          <option value="account">Account</option>
                          <option value="platform">Trading Platform</option>
                          <option value="challenge">Challenge</option>
                          <option value="payment">Payment</option>
                          <option value="withdrawal">Withdrawal</option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="priority" className="text-white">
                          Priority
                        </Label>
                        <select
                          id="priority"
                          className="w-full h-10 px-3 rounded-md bg-[#001a2c] border border-[#003a4c] text-white focus:outline-none focus:ring-2 focus:ring-teal-500"
                          required
                        >
                          <option value="">Select priority</option>
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-white">
                        Message
                      </Label>
                      <Textarea
                        id="message"
                        placeholder="Describe your issue in detail"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        className="min-h-32 bg-[#001a2c] border-[#003a4c] text-white"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="attachments" className="text-white">
                        Attachments (Optional)
                      </Label>
                      <Input
                        id="attachments"
                        type="file"
                        multiple
                        className="bg-[#001a2c] border-[#003a4c] text-white"
                      />
                      <p className="text-xs text-gray-400">
                        You can upload up to 3 files (max 5MB each). Supported formats: JPG, PNG, PDF.
                      </p>
                    </div>

                    <Button type="submit" className="bg-teal-500 hover:bg-teal-600 text-white">
                      Submit Ticket
                    </Button>
                  </form>
                </TabsContent>

                <TabsContent value="faq" className="mt-6">
                  <div className="mb-6">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                      <Input
                        placeholder="Search FAQs..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    {faqs.map((faq, index) => (
                      <div key={index} className="border border-[#003a4c] rounded-lg overflow-hidden bg-[#001a2c]">
                        <div className="p-4">
                          <div className="flex items-start">
                            <HelpCircle className="h-5 w-5 text-teal-500 mr-3 mt-0.5" />
                            <div>
                              <h3 className="text-white font-medium">{faq.question}</h3>
                              <p className="text-gray-400 mt-2">{faq.answer}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 text-center">
                    <p className="text-gray-400 mb-4">Can't find what you're looking for?</p>
                    <Button className="bg-teal-500 hover:bg-teal-600 text-white">Browse Knowledge Base</Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Contact Information</CardTitle>
              <CardDescription className="text-gray-400">Alternative ways to reach our support team</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-teal-500 mr-3 mt-0.5" />
                  <div>
                    <h3 className="text-white font-medium">Email Support</h3>
                    <p className="text-gray-400 mb-1">For general inquiries:</p>
                    <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-teal-500 mr-3 mt-0.5" />
                  <div>
                    <h3 className="text-white font-medium">Phone Support</h3>
                    <p className="text-gray-400 mb-1">Available Monday-Friday, 9AM-5PM ET:</p>
                    <a href="tel:+***********" className="text-teal-400 hover:underline">
                      +****************
                    </a>
                  </div>
                </div>

                <div className="flex items-start">
                  <MessageSquare className="h-5 w-5 text-teal-500 mr-3 mt-0.5" />
                  <div>
                    <h3 className="text-white font-medium">Live Chat</h3>
                    <p className="text-gray-400 mb-1">Available during business hours:</p>
                    <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                      Start Chat
                    </Button>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-[#003a4c]">
                <h3 className="text-white font-medium mb-3">Support Hours</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Monday - Friday:</span>
                    <span className="text-white">9:00 AM - 5:00 PM ET</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Saturday:</span>
                    <span className="text-white">10:00 AM - 2:00 PM ET</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Sunday:</span>
                    <span className="text-white">Closed</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t border-[#003a4c] pt-6">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                <FileText className="mr-2 h-4 w-4" />
                View Knowledge Base
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Common Issues</CardTitle>
            <CardDescription className="text-gray-400">
              Quick solutions for frequently encountered problems
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  title: "Platform Connection Issues",
                  description:
                    "If you're having trouble connecting to the trading platform, try these troubleshooting steps.",
                  icon: <AlertCircle className="h-10 w-10 text-yellow-500" />,
                },
                {
                  title: "Account Verification",
                  description:
                    "Learn how to complete your KYC verification process and resolve common verification issues.",
                  icon: <CheckCircle className="h-10 w-10 text-green-500" />,
                },
                {
                  title: "Withdrawal Problems",
                  description:
                    "Understand common withdrawal issues and how to ensure your withdrawal requests are processed smoothly.",
                  icon: <XCircle className="h-10 w-10 text-red-500" />,
                },
              ].map((issue, index) => (
                <div key={index} className="flex flex-col p-6 bg-[#001a2c] rounded-lg border border-[#003a4c] h-full">
                  <div className="flex items-center mb-4">
                    {issue.icon}
                    <h3 className="text-white font-medium ml-3">{issue.title}</h3>
                  </div>
                  <p className="text-gray-400 text-sm mb-4">{issue.description}</p>
                  <Button
                    variant="outline"
                    className="mt-auto border-[#003a4c] text-white hover:bg-[#003a4c] justify-between"
                  >
                    <span>View Solutions</span>
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
