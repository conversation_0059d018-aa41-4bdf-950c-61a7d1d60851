"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  TrendingUp,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
} from "lucide-react"

export default function OrdersManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")

  const orders = [
    {
      id: "ORD-001",
      userId: "USR-123",
      userName: "<PERSON> Doe",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Standard Challenge",
      amount: "$100,000",
      status: "running",
      progress: "65%",
      profit: "+$4,250",
      drawdown: "1.2%",
      startDate: "2024-05-15",
      endDate: "2024-06-15",
      daysRemaining: 12,
    },
    {
      id: "ORD-002",
      userId: "USR-456",
      userName: "Jane Smith",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Express Challenge",
      amount: "$50,000",
      status: "completed",
      progress: "100%",
      profit: "+$8,450",
      drawdown: "2.1%",
      startDate: "2024-04-20",
      endDate: "2024-05-20",
      daysRemaining: 0,
    },
    {
      id: "ORD-003",
      userId: "USR-789",
      userName: "Mike Johnson",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "HFT Challenge",
      amount: "$200,000",
      status: "failed",
      progress: "45%",
      profit: "-$2,100",
      drawdown: "5.8%",
      startDate: "2024-03-10",
      endDate: "2024-04-10",
      daysRemaining: 0,
    },
    {
      id: "ORD-004",
      userId: "USR-321",
      userName: "Sarah Wilson",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Standard Challenge",
      amount: "$100,000",
      status: "passed",
      progress: "100%",
      profit: "+$12,890",
      drawdown: "1.8%",
      startDate: "2024-02-15",
      endDate: "2024-03-15",
      daysRemaining: 0,
    },
    {
      id: "ORD-005",
      userId: "USR-654",
      userName: "David Brown",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Express Challenge",
      amount: "$50,000",
      status: "stage2",
      progress: "30%",
      profit: "+$1,890",
      drawdown: "0.9%",
      startDate: "2024-05-20",
      endDate: "2024-06-20",
      daysRemaining: 18,
    },
    {
      id: "ORD-006",
      userId: "USR-987",
      userName: "Emma Davis",
      userAvatar: "/placeholder.svg?height=32&width=32",
      type: "Live Account",
      amount: "$100,000",
      status: "live",
      progress: "N/A",
      profit: "+$23,450",
      drawdown: "2.3%",
      startDate: "2024-01-10",
      endDate: "Ongoing",
      daysRemaining: "∞",
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "running":
        return (
          <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            <Play className="mr-1 h-3 w-3" />
            Running
          </Badge>
        )
      case "completed":
        return (
          <Badge className="bg-sky-500/10 text-sky-500 border-sky-500/20">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "passed":
        return (
          <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            <TrendingUp className="mr-1 h-3 w-3" />
            Passed
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-500/10 text-red-500 border-red-500/20">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      case "stage2":
        return (
          <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/20">
            <BarChart3 className="mr-1 h-3 w-3" />
            Stage 2
          </Badge>
        )
      case "live":
        return (
          <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">
            <Clock className="mr-1 h-3 w-3" />
            Live
          </Badge>
        )
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">Orders Management</h1>
            <p className="text-gray-400">Manage all trading orders and challenges</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button className="bg-sky-500 hover:bg-sky-600 text-white">
              <BarChart3 className="mr-2 h-4 w-4" />
              Create Order
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Stats Overview */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            { label: "Running", count: 456, icon: Play, color: "text-blue-500" },
            { label: "Completed", count: 1289, icon: CheckCircle, color: "text-sky-500" },
            { label: "Passed", count: 892, icon: TrendingUp, color: "text-blue-500" },
            { label: "Failed", count: 234, icon: XCircle, color: "text-red-500" },
            { label: "Stage 2", count: 167, icon: BarChart3, color: "text-purple-500" },
            { label: "Live", count: 89, icon: Clock, color: "text-yellow-500" },
          ].map((stat, index) => (
            <Card key={index} className="bg-[#002a3c] border-[#003a4c]">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <stat.icon className={`h-5 w-5 ${stat.color}`} />
                  <Badge variant="secondary" className="bg-[#001a2c] text-white">
                    {stat.count}
                  </Badge>
                </div>
                <h3 className="text-sm font-medium text-white mt-2">{stat.label}</h3>
              </CardContent>
            </Card>
          ))}
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search orders by ID, user, or type..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="passed">Passed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="stage2">Stage 2</SelectItem>
                  <SelectItem value="live">Live</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="standard">Standard Challenge</SelectItem>
                  <SelectItem value="express">Express Challenge</SelectItem>
                  <SelectItem value="hft">HFT Challenge</SelectItem>
                  <SelectItem value="live">Live Account</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                <Filter className="mr-2 h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Orders Table */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">All Orders ({orders.length})</CardTitle>
            <CardDescription className="text-gray-400">Complete list of trading orders and challenges</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-[#003a4c]">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b border-[#003a4c]">
                    <tr>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Order ID</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">User</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Type</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Amount</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Progress</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">P/L</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Drawdown</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Days Left</th>
                      <th className="h-12 px-4 text-right font-medium text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr key={order.id} className="border-b border-[#003a4c] hover:bg-[#001a2c]">
                        <td className="p-4">
                          <div className="font-medium text-white">{order.id}</div>
                          <div className="text-xs text-gray-400">{order.startDate}</div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={order.userAvatar || "/placeholder.svg"} alt={order.userName} />
                              <AvatarFallback className="bg-[#003a4c] text-white text-xs">
                                {order.userName
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-white text-sm">{order.userName}</div>
                              <div className="text-xs text-gray-400">{order.userId}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{order.type}</div>
                        </td>
                        <td className="p-4">
                          <div className="font-medium text-white">{order.amount}</div>
                        </td>
                        <td className="p-4">{getStatusBadge(order.status)}</td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{order.progress}</div>
                        </td>
                        <td className="p-4">
                          <div
                            className={`font-medium ${order.profit.startsWith("+") ? "text-sky-500" : "text-red-500"}`}
                          >
                            {order.profit}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{order.drawdown}</div>
                        </td>
                        <td className="p-4">
                          <div className="text-sm text-gray-300">{order.daysRemaining}</div>
                        </td>
                        <td className="p-4 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-[#002a3c] border-[#003a4c] text-white">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Order
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Download className="mr-2 h-4 w-4" />
                                Export Data
                              </DropdownMenuItem>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c] text-red-400">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Cancel Order
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
