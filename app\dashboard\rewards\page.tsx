"use client"

import type React from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Gift, DollarSign, Award, Trophy, Clock, Users, TrendingUp, Calendar, CheckCircle } from "lucide-react"

export default function RewardsPage() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div variants={fadeInUp} initial="hidden" animate="visible">
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Available Rewards</CardTitle>
              <div className="flex items-center">
                <Gift className="h-5 w-5 text-teal-500 mr-2" />
                <span className="text-2xl font-bold text-white">1,250</span>
                <span className="text-sm text-gray-400 ml-2">points</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-400">
                You can redeem your points for trading credits, account upgrades, and more.
              </p>
            </CardContent>
            <CardFooter className="pt-0">
              <Button className="w-full bg-teal-500 hover:bg-teal-600 text-white">Redeem Points</Button>
            </CardFooter>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Loyalty Level</CardTitle>
              <div className="flex items-center">
                <Award className="h-5 w-5 text-teal-500 mr-2" />
                <span className="text-2xl font-bold text-white">Silver</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Progress to Gold</span>
                  <span className="text-sm font-medium text-white">65%</span>
                </div>
                <Progress value={65} className="h-2 bg-gray-700">
                  <div className="h-full bg-teal-500" style={{ width: "65%" }} />
                </Progress>
                <p className="text-xs text-gray-400">Earn 2,500 more points to reach Gold level</p>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                View Benefits
              </Button>
            </CardFooter>
          </Card>
        </motion.div>

        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Next Reward</CardTitle>
              <div className="flex items-center">
                <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                <span className="text-2xl font-bold text-white">$50 Credit</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Progress</span>
                  <span className="text-sm font-medium text-white">80%</span>
                </div>
                <Progress value={80} className="h-2 bg-gray-700">
                  <div className="h-full bg-yellow-500" style={{ width: "80%" }} />
                </Progress>
                <div className="flex items-center text-xs text-gray-400">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Complete 2 more trades to unlock</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                View All Rewards
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Rewards Catalog</CardTitle>
            <CardDescription className="text-gray-400">Redeem your points for these exclusive rewards</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  title: "$50 Trading Credit",
                  description: "Add $50 to your trading account balance",
                  points: 500,
                  popular: true,
                  icon: <DollarSign className="h-10 w-10 text-teal-500" />,
                },
                {
                  title: "Account Upgrade",
                  description: "Upgrade your account size by 25%",
                  points: 2000,
                  popular: false,
                  icon: <TrendingUp className="h-10 w-10 text-teal-500" />,
                },
                {
                  title: "1-on-1 Coaching",
                  description: "30-minute session with a professional trader",
                  points: 1500,
                  popular: false,
                  icon: <Users className="h-10 w-10 text-teal-500" />,
                },
                {
                  title: "Premium Indicators",
                  description: "Access to our suite of premium trading indicators",
                  points: 1000,
                  popular: false,
                  icon: <Chart className="h-10 w-10 text-teal-500" />,
                },
                {
                  title: "Challenge Discount",
                  description: "25% off your next trading challenge",
                  points: 1200,
                  popular: true,
                  icon: <Gift className="h-10 w-10 text-teal-500" />,
                },
                {
                  title: "Extended Account Access",
                  description: "Add 30 days to your current challenge period",
                  points: 800,
                  popular: false,
                  icon: <Calendar className="h-10 w-10 text-teal-500" />,
                },
              ].map((reward, index) => (
                <Card
                  key={index}
                  className={`bg-[#001a2c] border-[#003a4c] ${reward.popular ? "border-teal-500 border-2" : ""} h-full`}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{reward.title}</CardTitle>
                      {reward.popular && <Badge className="bg-teal-500 text-white">Popular</Badge>}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center mb-4">
                      {reward.icon}
                      <div className="ml-4">
                        <p className="text-gray-400">{reward.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Award className="h-5 w-5 text-yellow-500 mr-2" />
                        <span className="text-white font-bold">{reward.points} points</span>
                      </div>
                      <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                        Redeem
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.4 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Ways to Earn Points</CardTitle>
            <CardDescription className="text-gray-400">
              Complete these activities to earn more reward points
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  title: "Complete a Trading Challenge",
                  description: "Successfully pass any trading challenge",
                  points: 500,
                  icon: <Trophy className="h-5 w-5 text-sky-500 mr-2" />,
                  completed: true,
                },
                {
                  title: "Refer a Friend",
                  description: "Earn points when your referral purchases a challenge",
                  points: 300,
                  icon: <Users className="h-5 w-5 text-sky-500 mr-2" />,
                  completed: true,
                },
                {
                  title: "Achieve Profit Target",
                  description: "Reach your profit target in any challenge",
                  points: 400,
                  icon: <TrendingUp className="h-5 w-5 text-sky-500 mr-2" />,
                  completed: false,
                },
                {
                  title: "Complete Educational Course",
                  description: "Finish any course in our trading academy",
                  points: 200,
                  icon: <BookOpen className="h-5 w-5 text-sky-500 mr-2" />,
                  completed: false,
                },
                {
                  title: "Trade Consistently",
                  description: "Place at least one trade every day for 7 consecutive days",
                  points: 150,
                  icon: <Calendar className="h-5 w-5 text-teal-500 mr-2" />,
                  completed: false,
                },
              ].map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border border-[#003a4c] rounded-lg bg-[#001a2c]"
                >
                  <div className="flex items-center">
                    {activity.icon}
                    <div>
                      <p className="text-white font-medium">{activity.title}</p>
                      <p className="text-sm text-gray-400">{activity.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="flex items-center mr-4">
                      <Award className="h-4 w-4 text-yellow-500 mr-1" />
                      <span className="text-white font-medium">{activity.points}</span>
                    </div>
                    {activity.completed ? (
                      <Badge className="bg-green-500/20 text-green-400">Completed</Badge>
                    ) : (
                      <Button variant="outline" size="sm" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                        Start
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.5 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Loyalty Program Benefits</CardTitle>
            <CardDescription className="text-gray-400">Exclusive benefits based on your loyalty level</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                {
                  level: "Bronze",
                  points: "0 - 1,000",
                  benefits: [
                    "Access to basic educational content",
                    "Standard customer support",
                    "Regular trading conditions",
                  ],
                  current: false,
                },
                {
                  level: "Silver",
                  points: "1,001 - 3,500",
                  benefits: [
                    "5% discount on all challenges",
                    "Priority customer support",
                    "Access to weekly webinars",
                    "Exclusive market analysis",
                  ],
                  current: true,
                },
                {
                  level: "Gold",
                  points: "3,501 - 7,500",
                  benefits: [
                    "10% discount on all challenges",
                    "VIP customer support",
                    "Monthly 1-on-1 coaching session",
                    "Reduced drawdown limits",
                    "Higher profit splits",
                  ],
                  current: false,
                },
                {
                  level: "Platinum",
                  points: "7,501+",
                  benefits: [
                    "15% discount on all challenges",
                    "24/7 dedicated account manager",
                    "Weekly 1-on-1 coaching sessions",
                    "Custom challenge parameters",
                    "Highest profit splits",
                    "Early access to new features",
                  ],
                  current: false,
                },
              ].map((tier, index) => (
                <Card
                  key={index}
                  className={`bg-[#001a2c] border-[#003a4c] ${tier.current ? "border-teal-500 border-2" : ""} h-full`}
                >
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-white">{tier.level}</CardTitle>
                      {tier.current && <Badge className="bg-teal-500 text-white">Current</Badge>}
                    </div>
                    <CardDescription className="text-gray-400">{tier.points} points</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {tier.benefits.map((benefit, i) => (
                        <li key={i} className="flex items-start text-gray-300">
                          <CheckCircle className="h-5 w-5 text-teal-500 mr-2 shrink-0" />
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              Points are valid for 12 months from the date they are earned. For more information about our rewards
              program, please visit our{" "}
              <a href="/rewards-faq" className="text-teal-400 hover:underline">
                Rewards FAQ
              </a>
              .
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}

function Chart(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 3v18h18" />
      <path d="M18 17V9" />
      <path d="M13 17V5" />
      <path d="M8 17v-3" />
    </svg>
  )
}

function BookOpen(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
    </svg>
  )
}
