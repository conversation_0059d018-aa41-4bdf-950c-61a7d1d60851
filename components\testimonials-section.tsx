"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Crown, Diamond, Quote, Award, TrendingUp } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Elite Professional Trader",
      content:
        "After trying several funding programs, Funded Whales stands out with their transparent rules and exceptional support. I passed the evaluation in just 2 weeks and have been consistently profitable since. The platform's sophistication is unmatched.",
      rating: 5,
      location: "London, UK",
      profit: "$127,500",
      badge: "Elite",
    },
    {
      name: "<PERSON>",
      role: "High-Frequency Day Trader",
      content:
        "The HFT Neo program is perfect for my scalping strategy. Ultra-low latency execution and the 90% profit split make this the best funding option I've found. Absolutely revolutionary platform!",
      rating: 5,
      location: "Sydney, Australia",
      profit: "$89,200",
      badge: "Premium",
    },
    {
      name: "<PERSON>",
      role: "Institutional Swing Trader",
      content:
        "What impressed me most was how quickly I received my first payout. The process was seamless, and the funds were in my account within 24 hours. Their educational resources and analytics are world-class.",
      rating: 5,
      location: "Toronto, Canada",
      profit: "$156,800",
      badge: "Elite",
    },
    {
      name: "Liam D.",
      role: "Quantitative Day Trader",
      content:
        "I wasn't sure what to expect, but the evaluation was smooth and fair. I passed in 12 days, and the funding came through without any issues. The platform's technology is cutting-edge.",
      rating: 5,
      location: "Manchester, UK",
      profit: "$73,400",
      badge: "Premium",
    },
    {
      name: "Priya M.",
      role: "Algorithmic Futures Trader",
      content:
        "What stood out was the execution speed and how clear the rules were. No hidden fees or surprises. Just solid support and a platform that delivers on every promise. Exceptional experience.",
      rating: 5,
      location: "Bangalore, India",
      profit: "$94,600",
      badge: "Elite",
    },
    {
      name: "Carlos R.",
      role: "Professional Forex Trader",
      content:
        "The payout process was straightforward and lightning-fast. I hit my first target and got paid in under 24 hours. No drama — just exactly what they promised. Premium service all the way.",
      rating: 5,
      location: "Mexico City, Mexico",
      profit: "$112,300",
      badge: "Premium",
    },
  ]

  return (
    <section className="py-28 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-25, -100],
              opacity: [0, 0.7, 0],
              scale: [0.4, 1.1, 0.4],
            }}
            transition={{
              duration: 7 + Math.random() * 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 7,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-6">
            <Award className="h-8 w-8 text-sky-400 mr-3" />
            <span className="text-sky-400 font-semibold tracking-wider uppercase text-sm">Success Stories</span>
            <Award className="h-8 w-8 text-sky-400 ml-3" />
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            Elite{" "}
            <span className="bg-gradient-to-r from-sky-400 via-blue-400 to-sky-500 bg-clip-text text-transparent">
              Trader Success
            </span>{" "}
            Stories
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-sky-400 to-blue-400 mx-auto mb-6"></div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Discover how our elite traders have achieved extraordinary success with our premium funding platform
          </p>
        </motion.div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 6000,
            }),
          ]}
          className="w-full max-w-6xl mx-auto"
        >
          <CarouselContent className="-ml-4">
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="pl-4 md:basis-1/2 lg:basis-1/3">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group h-full"
                >
                  <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full backdrop-blur-sm hover:border-sky-400/40 transition-all duration-500 relative overflow-hidden">
                    {/* Premium badge */}
                    <div className="absolute top-4 right-4">
                      <div className={`flex items-center ${testimonial.badge === 'Elite' ? 'bg-gradient-to-r from-sky-500/20 to-blue-500/20' : 'bg-gradient-to-r from-blue-500/20 to-sky-500/20'} backdrop-blur-sm border border-sky-400/30 rounded-full px-3 py-1`}>
                        {testimonial.badge === 'Elite' ? (
                          <Crown className="h-3 w-3 text-sky-400 mr-1" />
                        ) : (
                          <Diamond className="h-3 w-3 text-sky-400 mr-1" />
                        )}
                        <span className="text-xs text-sky-400 font-semibold">{testimonial.badge}</span>
                      </div>
                    </div>

                    {/* Luxury glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-sky-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Quote decoration */}
                    <div className="absolute top-6 left-6">
                      <Quote className="h-8 w-8 text-sky-400/20" />
                    </div>

                    <CardContent className="p-8 relative z-10">
                      {/* Rating stars */}
                      <div className="flex items-center mb-6 mt-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400 mr-1" />
                        ))}
                      </div>

                      {/* Testimonial content */}
                      <p className="text-gray-300 mb-6 italic leading-relaxed text-base group-hover:text-gray-200 transition-colors duration-300">
                        "{testimonial.content}"
                      </p>

                      {/* Profit highlight */}
                      <div className="mb-6 p-3 bg-gradient-to-r from-sky-500/10 to-blue-500/10 rounded-lg border border-sky-400/20">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-sky-400 font-semibold">Total Profits</span>
                          <div className="flex items-center">
                            <TrendingUp className="h-4 w-4 text-sky-400 mr-2" />
                            <span className="text-lg font-bold text-sky-400">{testimonial.profit}</span>
                          </div>
                        </div>
                      </div>

                      {/* Trader info */}
                      <div className="border-t border-sky-500/20 pt-6">
                        <div className="flex items-start justify-between">
                          <div>
                            <p className="font-bold text-white text-lg group-hover:text-sky-100 transition-colors duration-300">
                              {testimonial.name}
                            </p>
                            <p className="text-sm text-sky-400 font-semibold mb-1">{testimonial.role}</p>
                            <p className="text-xs text-gray-400">{testimonial.location}</p>
                          </div>
                          <div className="w-12 h-12 bg-gradient-to-br from-sky-500/20 to-blue-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-sky-400/30">
                            <span className="text-sky-400 font-bold text-lg">{testimonial.name.charAt(0)}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>

                    {/* Luxury shine effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
          <CarouselNext className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
        </Carousel>

        {/* Luxury bottom stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-sky-500/10 to-blue-500/10 backdrop-blur-sm border border-sky-400/20 rounded-2xl px-8 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">$2.4M+</div>
              <div className="text-gray-300 font-semibold">Total Profits Paid</div>
            </div>
            <div className="bg-gradient-to-r from-blue-500/10 to-sky-500/10 backdrop-blur-sm border border-sky-400/20 rounded-2xl px-8 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">10,000+</div>
              <div className="text-gray-300 font-semibold">Elite Traders</div>
            </div>
            <div className="bg-gradient-to-r from-sky-500/10 to-blue-500/10 backdrop-blur-sm border border-sky-400/20 rounded-2xl px-8 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">98%</div>
              <div className="text-gray-300 font-semibold">Satisfaction Rate</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
