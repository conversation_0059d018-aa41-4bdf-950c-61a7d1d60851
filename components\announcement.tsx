"use client"

import { useState } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function Announcement() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <div className="relative bg-gradient-to-r from-sky-500 to-blue-600 text-white py-3 px-4">
      <div className="container mx-auto flex items-center justify-center">
        <div className="text-center flex items-center space-x-2">
          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-white/20">ANNOUNCEMENT</span>
          <p className="text-sm">
            We have temporarily paused operations due to system maintenance. We are currently exploring solutions. Mean
            while all operations are paused until further notice.
          </p>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-4 text-white hover:bg-white/20 hover:text-white"
          onClick={() => setIsVisible(false)}
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>
    </div>
  )
}
