"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Professional Trader",
      content:
        "After trying several funding programs, Funded Whales stands out with their transparent rules and excellent support. I passed the evaluation in just 2 weeks and have been consistently profitable since.",
      rating: 5,
      location: "London, UK",
    },
    {
      name: "<PERSON>",
      role: "Day Trader",
      content:
        "The HFT Neo program is perfect for my scalping strategy. Low latency execution and the 90% profit split make this the best funding option I've found. Highly recommended!",
      rating: 5,
      location: "Sydney, Australia",
    },
    {
      name: "<PERSON>",
      role: "Swing Trader",
      content:
        "What impressed me most was how quickly I received my first payout. The process was smooth, and the funds were in my account within 24 hours. Their educational resources are top-notch too.",
      rating: 4,
      location: "Toronto, Canada",
    },
    {
      name: "<PERSON>",
      role: "Day Trader",
      content:
        "I wasn't sure what to expect, but the evaluation was smooth and fair. I passed in 12 days, and the funding came through without any issues.",
      rating: 5,
      location: "Manchester, UK",
    },
    {
      name: "Priya M.",
      role: "Futures Trader",
      content:
        "What stood out was the execution speed and how clear the rules were. No hidden stuff. Just solid support and a platform that works.",
      rating: 5,
      location: "Bangalore, India",
    },
    {
      name: "Carlos R.",
      role: "Forex Trader",
      content:
        "The payout process was straightforward. I hit my first target and got paid in under 24 hours. No drama — just exactly what they promised.",
      rating: 5,
      location: "Mexico City, Mexico",
    },
  ]

  return (
    <section className="py-20 bg-[#001a2c]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Trader Success Stories</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Hear from traders who have successfully passed our challenges and are now trading with our capital
          </p>
        </motion.div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 4000,
            }),
          ]}
          className="w-full max-w-7xl mx-auto"
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="h-full"
                >
                  <Card className="bg-[#002a3c] border-[#003a4c] h-full">
                    <CardContent className="p-6">
                      <div className="flex mb-4">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-gray-300 mb-6">"{testimonial.content}"</p>
                      <div className="mt-auto">
                        <p className="font-semibold text-white">{testimonial.name}</p>
                        <div className="flex justify-between items-center mt-1">
                          <p className="text-sm text-gray-400">{testimonial.role}</p>
                          <p className="text-xs text-gray-500">{testimonial.location}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex -left-12 bg-[#002a3c] border-[#003a4c] text-white hover:bg-[#003a4c]" />
          <CarouselNext className="hidden md:flex -right-12 bg-[#002a3c] border-[#003a4c] text-white hover:bg-[#003a4c]" />
        </Carousel>
      </div>
    </section>
  )
}
