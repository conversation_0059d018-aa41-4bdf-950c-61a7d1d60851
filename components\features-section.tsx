"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap, Clock, Shield, BarChart2, Award, Percent } from "lucide-react"

export default function FeaturesSection() {
  const features = [
    {
      icon: <Zap className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "HFT Friendly",
      description: "Designed specifically for high-frequency traders with low latency and fast execution.",
    },
    {
      icon: <Clock className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "Instant Verification",
      description: "Get verified and start trading within minutes, not days.",
    },
    {
      icon: <Shield className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "Secure Platform",
      description: "Enterprise-grade security to protect your account and trading data.",
    },
    {
      icon: <BarChart2 className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "Advanced Analytics",
      description: "Comprehensive trading analytics to improve your performance.",
    },
    {
      icon: <Award className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "Competitive Challenges",
      description: "Prove your skills and earn funding through our trading challenges.",
    },
    {
      icon: <Percent className="h-10 w-10 text-sky-400" />, // Changed from teal to sky
      title: "High Profit Split",
      description: "Earn up to 90% of your trading profits with our industry-leading profit split.",
    },
  ]

  return (
    <section className="py-20 bg-[#001a2c]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
            Why Traders Choose <span className="text-sky-400">Funded Whales</span> {/* Changed from teal to sky */}
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Our platform offers unparalleled advantages designed to help you succeed as a trader.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="bg-[#002a3c] border-[#003a4c] h-full">
                <CardHeader>
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle className="text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
