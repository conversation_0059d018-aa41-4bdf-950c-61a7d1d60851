"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Filter,
  MoreHorizontal,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Calendar,
  DollarSign,
  Shield,
  Eye,
  Edit,
  Trash2,
} from "lucide-react"

export default function UsersManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [kycFilter, setKycFilter] = useState("all")

  const users = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-01-15",
      status: "active",
      kycStatus: "verified",
      accountBalance: "$98,450",
      totalOrders: 12,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-02-20",
      status: "active",
      kycStatus: "pending",
      accountBalance: "$45,230",
      totalOrders: 8,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "3",
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-01-08",
      status: "suspended",
      kycStatus: "verified",
      accountBalance: "$12,890",
      totalOrders: 15,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "4",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-03-10",
      status: "active",
      kycStatus: "rejected",
      accountBalance: "$67,120",
      totalOrders: 6,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "5",
      name: "David Brown",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-02-28",
      status: "inactive",
      kycStatus: "not_submitted",
      accountBalance: "$23,450",
      totalOrders: 3,
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-sky-500/10 text-sky-500 border-sky-500/20">Active</Badge>
      case "inactive":
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Inactive</Badge>
      case "suspended":
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Suspended</Badge>
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  const getKycBadge = (kycStatus: string) => {
    switch (kycStatus) {
      case "verified":
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">Verified</Badge>
      case "pending":
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">Pending</Badge>
      case "rejected":
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Rejected</Badge>
      case "not_submitted":
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Not Submitted</Badge>
      default:
        return <Badge className="bg-gray-500/10 text-gray-500 border-gray-500/20">Unknown</Badge>
    }
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">User Management</h1>
            <p className="text-gray-400">Manage all registered users and their accounts</p>
          </div>
          <Button className="bg-sky-500 hover:bg-sky-600 text-white">
            <UserCheck className="mr-2 h-4 w-4" />
            Add New User
          </Button>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.1 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search users by name, email, or phone..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
              <Select value={kycFilter} onValueChange={setKycFilter}>
                <SelectTrigger className="w-[180px] bg-[#001a2c] border-[#003a4c] text-white">
                  <SelectValue placeholder="KYC Status" />
                </SelectTrigger>
                <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                  <SelectItem value="all">All KYC</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="not_submitted">Not Submitted</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c]">
                <Filter className="mr-2 h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Users Table */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">All Users ({users.length})</CardTitle>
            <CardDescription className="text-gray-400">Complete list of registered users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-[#003a4c]">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b border-[#003a4c]">
                    <tr>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">User</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Contact</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Join Date</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Status</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">KYC</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Balance</th>
                      <th className="h-12 px-4 text-left font-medium text-gray-400">Orders</th>
                      <th className="h-12 px-4 text-right font-medium text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id} className="border-b border-[#003a4c] hover:bg-[#001a2c]">
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                              <AvatarFallback className="bg-[#003a4c] text-white">
                                {user.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-white">{user.name}</div>
                              <div className="text-sm text-gray-400">ID: {user.id}</div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-gray-300">
                              <Mail className="mr-2 h-3 w-3" />
                              {user.email}
                            </div>
                            <div className="flex items-center text-sm text-gray-300">
                              <Phone className="mr-2 h-3 w-3" />
                              {user.phone}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center text-sm text-gray-300">
                            <Calendar className="mr-2 h-3 w-3" />
                            {user.joinDate}
                          </div>
                        </td>
                        <td className="p-4">{getStatusBadge(user.status)}</td>
                        <td className="p-4">{getKycBadge(user.kycStatus)}</td>
                        <td className="p-4">
                          <div className="flex items-center text-sm font-medium text-white">
                            <DollarSign className="mr-1 h-3 w-3" />
                            {user.accountBalance}
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge variant="secondary" className="bg-[#001a2c] text-white">
                            {user.totalOrders}
                          </Badge>
                        </td>
                        <td className="p-4 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-[#002a3c] border-[#003a4c] text-white">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Edit className="mr-2 h-4 w-4" />
                                Edit User
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Shield className="mr-2 h-4 w-4" />
                                Manage KYC
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c]">
                                <Mail className="mr-2 h-4 w-4" />
                                Send Message
                              </DropdownMenuItem>
                              <DropdownMenuSeparator className="bg-[#003a4c]" />
                              <DropdownMenuItem className="hover:bg-[#003a4c] text-red-400">
                                <UserX className="mr-2 h-4 w-4" />
                                Suspend User
                              </DropdownMenuItem>
                              <DropdownMenuItem className="hover:bg-[#003a4c] text-red-400">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete User
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
