"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"

export default function HowItWorksSection() {
  const steps = [
    {
      number: "01",
      title: "Choose Your Challenge",
      description: "Select from our range of account sizes and challenge parameters that suit your trading style.",
    },
    {
      number: "02",
      title: "Pass the Challenge",
      description:
        "Demonstrate your trading skills by meeting the profit targets while adhering to our risk management rules.",
    },
    {
      number: "03",
      title: "Get Funded",
      description: "Once you pass the challenge, receive access to a funded account with our capital.",
    },
    {
      number: "04",
      title: "Earn Profits",
      description: "Trade with our capital and earn up to 90% of the profits you generate.",
    },
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-[#00253c] to-[#001a2c]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">How It Works</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Our simple four-step process to get you trading with our capital
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="bg-[#002a3c] border-[#003a4c] h-full relative overflow-hidden">
                <div className="absolute -top-6 -left-6 text-8xl font-bold text-[#003a4c] opacity-20">
                  {step.number}
                </div>
                <CardContent className="pt-8 pb-6 px-6 relative z-10">
                  <h3 className="text-xl font-bold mb-3 text-white">{step.title}</h3>
                  <p className="text-gray-400">{step.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
